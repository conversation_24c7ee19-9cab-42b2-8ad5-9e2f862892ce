# @fincloud/ui/tooltip

A comprehensive Angular tooltip component library providing contextual information overlays with advanced positioning, custom content support, and flexible configuration options for enhanced user experience.

## Components & Directives

### FinTooltipDirective (`finTooltip`)

The main tooltip directive that provides contextual information overlays with extensive customization and positioning capabilities.

**Key Features:**

- String and template content support
- Four positioning options (top, bottom, left, right)
- Automatic fallback positioning when space is limited
- Configurable open and close delays
- Optional arrow indicators
- Custom positioning targets
- Template context support for dynamic content
- Always visible mode for persistent tooltips
- Maximum width constraints
- Built on Angular CDK Overlay for optimal performance

**Basic Usage:**

```typescript
import { FinTooltipModule } from '@fincloud/ui/tooltip';

// Simple string tooltip
<button finTooltip content="Save your changes">
  Save
</button>

// Tooltip with custom placement
<button finTooltip
  content="Delete this item permanently"
  placement="bottom">
  Delete
</button>

// Tooltip with arrow
<button finTooltip
  content="Edit item details"
  placement="right"
  [showArrow]="true">
  Edit
</button>
```

**Template Content:**

```typescript
// Template-based tooltip content
<button finTooltip
  [content]="tooltipTemplate"
  [context]="{ user: currentUser }">
  User Info
</button>

<ng-template #tooltipTemplate let-user="user">
  <div class="user-tooltip">
    <strong>{{ user.name }}</strong>
    <p>{{ user.email }}</p>
    <small>Last login: {{ user.lastLogin | date }}</small>
  </div>
</ng-template>
```

**Advanced Configuration:**

```typescript
// Tooltip with delays and custom positioning
<div finTooltip
  content="This tooltip appears after 1 second"
  [openDelay]="1000"
  [closeDelay]="500"
  [maxWidth]="300"
  placement="top"
  [showArrow]="true">
  Hover for delayed tooltip
</div>

// Always visible tooltip
<div finTooltip
  content="This tooltip is always visible"
  [setAlwaysVisible]="true"
  placement="bottom">
  Persistent tooltip
</div>

// Disabled tooltip
<button finTooltip
  content="This won't show"
  [disableTooltip]="isTooltipDisabled">
  Conditional tooltip
</button>
```

### FinTooltipComponent (`fin-tooltip`)

The tooltip display component that renders the tooltip content with appropriate styling and positioning.

**Key Features:**

- String and template content rendering
- Arrow indicator support
- Responsive positioning
- Maximum width constraints
- Context-aware template rendering

## Positioning & Placement

### Placement Options

- `top` - Tooltip appears above the element (default)
- `bottom` - Tooltip appears below the element
- `left` - Tooltip appears to the left of the element
- `right` - Tooltip appears to the right of the element

### Automatic Fallbacks

The tooltip automatically adjusts its position when there's insufficient space:

- **Primary Position**: Uses the specified placement
- **Opposite Fallback**: Flips to the opposite side if no space
- **Horizontal/Vertical Fallbacks**: Uses perpendicular positions as last resort

### Custom Position Targets

Position tooltips relative to different elements:

```typescript
// Position relative to another element by ID
<div finTooltip
  content="Tooltip for button"
  positionTarget="#target-button">
  Hover here
</div>
<button id="target-button">Target</button>

// Position relative to element by class
<div finTooltip
  content="Tooltip for input"
  positionTarget=".target-input">
  Hover here
</div>
<input class="target-input" />

// Position relative to element reference
<div finTooltip
  content="Tooltip for element"
  [positionTarget]="targetElement">
  Hover here
</div>
<div #targetElement>Target</div>
```

## Configuration Options

### Timing Controls

- `openDelay` - Delay in milliseconds before showing (default: 250ms)
- `closeDelay` - Delay in milliseconds before hiding (default: 0ms)

### Visual Options

- `showArrow` - Display arrow pointing to target element
- `maxWidth` - Maximum width in pixels for tooltip content
- `placement` - Preferred positioning relative to target

### Behavior Options

- `setAlwaysVisible` - Keep tooltip permanently visible
- `disableTooltip` - Completely disable tooltip functionality
- `context` - Template context object for dynamic content

## Installation & Import

```typescript
import { FinTooltipModule } from '@fincloud/ui/tooltip';

// Or import individual components and directives
import { FinTooltipDirective, FinTooltipComponent } from '@fincloud/ui/tooltip';
```

## Integration Examples

### With Form Fields

```typescript
<fin-input
  label="Password"
  type="password"
  finTooltip
  content="Password must be at least 8 characters"
  placement="right"
  [showArrow]="true">
</fin-input>
```

### With Buttons

```typescript
<button fin-button-action
  size="s"
  finTooltip
  content="Delete item"
  placement="top">
  <fin-icon name="delete"></fin-icon>
</button>
```

### With Table Actions

```typescript
<ng-template name="actions" [finRowTemplate]="rows" let-row>
  <button fin-button-action
    size="s"
    finTooltip
    content="Edit {{ row.name }}"
    placement="top"
    (click)="edit(row)">
    <fin-icon name="edit"></fin-icon>
  </button>
</ng-template>
```

### With Progress Bars

```typescript
<fin-progress-bar [segments]="segments">
  <div finTooltip
    [content]="segmentTooltip"
    [context]="{ segment: segment }"
    placement="top"
    [showArrow]="true">
    Progress segment
  </div>
</fin-progress-bar>
```

## Advanced Features

### Template Context

Pass dynamic data to template-based tooltips:

```typescript
<div *ngFor="let item of items"
  finTooltip
  [content]="itemTooltip"
  [context]="{ item: item, index: i }">
  {{ item.name }}
</div>

<ng-template #itemTooltip let-item="item" let-index="index">
  <div>
    <strong>{{ item.name }}</strong>
    <p>Position: {{ index + 1 }}</p>
    <small>{{ item.description }}</small>
  </div>
</ng-template>
```

### Conditional Tooltips

```typescript
<button finTooltip
  [content]="getTooltipContent()"
  [disableTooltip]="!shouldShowTooltip"
  placement="top">
  Dynamic Tooltip
</button>
```

### Performance Optimization

- **Lazy Loading**: Tooltips are created only when needed
- **CDK Overlay**: Efficient overlay management with Angular CDK
- **Position Caching**: Optimized position calculations
- **Memory Management**: Automatic cleanup when components are destroyed

### Accessibility

- **ARIA Support**: Proper ARIA attributes for screen readers
- **Keyboard Navigation**: Tooltip shows on focus for keyboard users
- **High Contrast**: Supports high contrast mode
- **Screen Reader**: Content is announced to assistive technologies
