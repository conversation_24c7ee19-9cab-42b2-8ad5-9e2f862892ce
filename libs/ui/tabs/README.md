# @fincloud/ui/tabs

A comprehensive Angular tabs component library providing tabbed navigation interfaces with customizable styling, content projection, and advanced features like badge integration and dynamic tab management.

## Components

### FinTabsComponent (`fin-tabs`)

The main tabs container component that manages tab navigation and content display with extensive customization options.

**Key Features:**

- Multiple tab types (primary, secondary)
- Two size variants (L, XL)
- Stretchable tabs to fill available width
- Dynamic tab index management
- Focus and selection event handling
- Built on Angular Material Tabs
- Accessibility support with proper ARIA attributes

**Basic Usage:**

```typescript
import { FinTabsModule } from '@fincloud/ui/tabs';

// In template
<fin-tabs
  [type]="finTabType.PRIMARY"
  [size]="finSize.XL"
  [selectedIndex]="0"
  (selectedIndexChange)="onTabChange($event)">

  <fin-tab>
    <ng-template finTabLabel>
      Tab 1
    </ng-template>
    <ng-template finTabBody>
      <p>Content for tab 1</p>
    </ng-template>
  </fin-tab>

  <fin-tab>
    <ng-template finTabLabel>
      Tab 2
    </ng-template>
    <ng-template finTabBody>
      <p>Content for tab 2</p>
    </ng-template>
  </fin-tab>

  <fin-tab disabled>
    <ng-template finTabLabel>
      Tab 3 (Disabled)
    </ng-template>
    <ng-template finTabBody>
      <p>Content for tab 3</p>
    </ng-template>
  </fin-tab>
</fin-tabs>
```

### FinTabComponent (`fin-tab`)

Individual tab component that defines a single tab with its label and content.

**Key Features:**

- Custom label and body content projection
- Disabled state support
- Hidden tab functionality
- Template-based content definition

**Basic Usage:**

```typescript
<fin-tab [disabled]="false">
  <ng-template finTabLabel>
    Custom Tab Label
  </ng-template>
  <ng-template finTabBody>
    <div class="tab-content">
      <!-- Your tab content here -->
    </div>
  </ng-template>
</fin-tab>
```

## Template Directives

### FinTabLabelDirective (`finTabLabel`)

Defines the content for tab labels with support for complex layouts and components.

**Usage:**

```typescript
<ng-template finTabLabel>
  <div class="fin-flex fin-flex-row fin-gap-[0.8rem]">
    <span>Tab with Badge</span>
    <fin-badge-indicator
      [count]="9"
      type="Default">
    </fin-badge-indicator>
  </div>
</ng-template>
```

### FinTabBodyDirective (`finTabBody`)

Defines the content area for each tab with full content projection support.

**Usage:**

```typescript
<ng-template finTabBody>
  <div class="tab-content-wrapper">
    <h3>Tab Content Title</h3>
    <p>Your tab content goes here...</p>
    <fin-button appearance="primary">Action Button</fin-button>
  </div>
</ng-template>
```

## Configuration Options

### Tab Types

- `FinTabType.PRIMARY` - Primary tab styling (default)
- `FinTabType.SECONDARY` - Secondary tab styling

### Tab Sizes

- `FinSize.L` - Large tabs
- `FinSize.XL` - Extra large tabs (default)

### Tab Behavior

- `selectedIndex` - Index of the currently selected tab
- `stretchTabs` - Whether tabs should fill the available width
- `dynamicIndex` - Whether to maintain tab index when tab elements change

## Installation & Import

```typescript
import { FinTabsModule } from '@fincloud/ui/tabs';

// Or import individual components and directives
import { FinTabsComponent, FinTabComponent, FinTabLabelDirective, FinTabBodyDirective } from '@fincloud/ui/tabs';
```

## Advanced Usage Examples

### Tabs with Badges

```typescript
<fin-tabs #finTabs [type]="finTabType.SECONDARY">
  <fin-tab>
    <ng-template finTabLabel>
      <div class="fin-flex fin-flex-row fin-gap-[0.8rem]">
        <span>Notifications</span>
        <fin-badge-indicator
          [count]="unreadCount"
          [type]="finTabs.selectedIndex !== 0 ? 'Default' : 'Active'">
        </fin-badge-indicator>
      </div>
    </ng-template>
    <ng-template finTabBody>
      <!-- Notifications content -->
    </ng-template>
  </fin-tab>
</fin-tabs>
```

### Tabs with Text Truncation

```typescript
<fin-tabs>
  <fin-tab>
    <ng-template finTabLabel>
      <span finTruncateText class="fin-max-w-[220px]">
        Very Long Tab Name That Needs Truncation
      </span>
    </ng-template>
    <ng-template finTabBody>
      <!-- Tab content -->
    </ng-template>
  </fin-tab>
</fin-tabs>
```

### Dynamic Tab Management

```typescript
// In component
selectedTabIndex = 0;
tabs = [
  { label: 'Tab 1', content: 'Content 1' },
  { label: 'Tab 2', content: 'Content 2' },
  { label: 'Tab 3', content: 'Content 3' }
];

onTabChange(index: number) {
  this.selectedTabIndex = index;
  // Handle tab change logic
}

// In template
<fin-tabs
  [selectedIndex]="selectedTabIndex"
  [dynamicIndex]="true"
  (selectedIndexChange)="onTabChange($event)">

  <fin-tab *ngFor="let tab of tabs">
    <ng-template finTabLabel>
      {{ tab.label }}
    </ng-template>
    <ng-template finTabBody>
      {{ tab.content }}
    </ng-template>
  </fin-tab>
</fin-tabs>
```

## Event Handling

### Available Events

- `selectedIndexChange` - Emitted when the selected tab index changes
- `selectedTabChange` - Emitted when the selected tab changes
- `focusChange` - Emitted when focus changes within tabs

### Event Usage

```typescript
<fin-tabs
  (selectedIndexChange)="onIndexChange($event)"
  (selectedTabChange)="onTabChange($event)"
  (focusChange)="onFocusChange($event)">
  <!-- tabs content -->
</fin-tabs>
```

## Advanced Features

### Accessibility

- **ARIA Support**: Full ARIA attributes for screen readers
- **Keyboard Navigation**: Standard keyboard interaction (arrow keys, space, enter)
- **Focus Management**: Proper focus indicators and management
- **Tab Semantics**: Proper tab panel semantics for assistive technologies

### Responsive Design

- **Flexible Sizing**: Tabs adapt to container width
- **Stretch Mode**: Option to fill available horizontal space
- **Text Truncation**: Built-in support for long tab labels
- **Mobile Friendly**: Touch-friendly interaction on mobile devices

### Performance

- **Change Detection**: OnPush strategy for optimal performance
- **Lazy Content**: Tab content is only rendered when active
- **Memory Management**: Proper cleanup and no memory leaks
- **Dynamic Updates**: Efficient handling of dynamic tab changes
