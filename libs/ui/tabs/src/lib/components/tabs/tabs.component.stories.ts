import { CommonModule } from '@angular/common';
import { FinBadgesModule } from '@fincloud/ui/badges';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinSize } from '@fincloud/ui/types';
import {
  Meta,
  StoryObj,
  componentWrapperDecorator,
  moduleMetadata,
} from '@storybook/angular';
import { FinTabType } from '../../enums/fin-tab-type';
import { FinTabsModule } from '../../tabs.module';
import { FinTabsComponent } from './tabs.component';

const meta: Meta<FinTabsComponent> = {
  component: FinTabsComponent,
  title: 'Components/Tabs',
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        FinTabsModule,
        FinBadgesModule,
        FinTruncateTextModule,
      ],
    }),
    componentWrapperDecorator(
      (story) => `<div class="fin-w-[900px]">${story}</div>`,
    ),
  ],
  parameters: {
    docs: {
      description: {
        component: '`import { FinTabsModule } from "@fincloud/ui/tabs"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=7116-7318&m=dev',
    },
  },
};

export default meta;
type Story = StoryObj<
  FinTabsComponent & {
    finTabLabel: string;
    finTabBody: string;

    // Tab
    disabled: boolean;
    isHidden: string;
  }
>;

const templateSecondary = `
      <fin-tabs
        #finTabs
        [type]="type"
        [size]="size"
      >
        <fin-tab>
          <ng-template finTabLabel>
           <div class="fin-flex fin-flex-row fin-gap-[0.8rem]">
            <span>Tab 1</span>
            <fin-badge-indicator
              [count]="9"
              [type]="finTabs.selectedIndex !== 0 ? 'Default' : 'Active'"
            ></fin-badge-indicator>
           </div>
          </ng-template>

          <ng-template finTabBody>
            Tab content 1
          </ng-template>
        </fin-tab>

        <fin-tab>
          <ng-template finTabLabel>
           <div class="fin-flex fin-flex-row fin-gap-[0.8rem]">
            <span>Tab 2</span>
            <fin-badge-indicator
              [count]="9"
              [type]="finTabs.selectedIndex !== 1 ? 'Default' : 'Active'"
            ></fin-badge-indicator>
           </div>
          </ng-template>

          <ng-template finTabBody>
            Tab content 2
          </ng-template>
        </fin-tab>

        <fin-tab disabled>
          <ng-template finTabLabel>
           <div class="fin-flex fin-flex-row fin-gap-[0.8rem]">
            <span>Tab 3</span>
            <fin-badge-indicator
              [count]="9"
              type="Inactive"
            ></fin-badge-indicator>
           </div>
          </ng-template>

          <ng-template finTabBody>
            Tab content 3
          </ng-template>
        </fin-tab>
      </fin-tabs>
    `;

export const Primary: Story = {
  args: {
    type: FinTabType.PRIMARY,
    size: FinSize.XL,
    selectedIndex: 0,
    stretchTabs: false,
  },
  argTypes: {
    type: {
      options: Object.values(FinTabType),
      control: { type: 'select' },
      defaultValue: FinTabType.PRIMARY,
      table: {
        category: 'Tabs',
      },
    },
    size: {
      options: [FinSize.L, FinSize.XL],
      control: { type: 'select' },
      defaultValue: FinSize.XL,
      table: {
        category: 'Tabs',
      },
    },
    selectedIndexChange: {
      control: false,
      table: {
        category: 'Tabs',
      },
    },
    selectedTabChange: {
      control: false,
      table: {
        category: 'Tabs',
      },
    },
    focusChange: {
      control: false,
      table: {
        category: 'Tabs',
      },
    },
    selectedIndex: {
      table: {
        category: 'Tabs',
      },
    },
    stretchTabs: {
      table: {
        category: 'Tabs',
      },
    },

    disabled: {
      description: 'Specifies if the tab is disabled.',
      control: { type: 'boolean' },
      defaultValue: { summary: 'false' },
      table: {
        category: 'Tab',
      },
    },
    isHidden: {
      description: 'Specifies if the tab is hidden.',
      control: { type: 'boolean' },
      defaultValue: { summary: 'false' },
      table: {
        category: 'Tab',
      },
    },
    finTabLabel: {
      description: 'Tab label template.',
      defaultValue: { summary: 'attribute' },
      table: {
        category: 'Templates',
      },
    },
    finTabBody: {
      description: 'Tab body template.',
      defaultValue: { summary: 'attribute' },
      table: {
        category: 'Templates',
      },
    },
  },
  render: (args) => ({
    props: args,
    template: `
    <fin-tabs
        [type]="type"
        [size]="size"
        [selectedIndex]="selectedIndex"
        [stretchTabs]="stretchTabs"
      >
        <fin-tab>
          <ng-template finTabLabel>
            <a href="#">Tasadasdab 1</a>
          </ng-template>

          <ng-template finTabBody>
            <a href="#">Tab content 1</a>
          </ng-template>
        </fin-tab>

        <fin-tab>
          <ng-template finTabLabel>
            <a href="#">Tab 2</a>
          </ng-template>

          <ng-template finTabBody>
            <a href="#">Tab content 2</a>
          </ng-template>
        </fin-tab>

        <fin-tab disabled>
          <ng-template finTabLabel>
            <a href="#">Tab 3</a>
          </ng-template>

          <ng-template finTabBody>
            <a href="#">Tab content 3</a>
          </ng-template>
        </fin-tab>
      </fin-tabs>
    `,
  }),
};

export const SecondaryL: Story = {
  name: 'Secondary - L',
  args: {
    type: FinTabType.SECONDARY,
    size: FinSize.L,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: args,
    template: templateSecondary,
  }),
};

export const SecondaryXL: Story = {
  name: 'Secondary - XL',
  args: {
    type: FinTabType.SECONDARY,
    size: FinSize.XL,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: args,
    template: templateSecondary,
  }),
};

export const SecondaryCompactL: Story = {
  name: 'Secondary Compact - L',
  args: {
    type: FinTabType.SECONDARY_COMPACT,
    size: FinSize.L,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: args,
    template: templateSecondary,
  }),
};

export const SecondaryCompactXL: Story = {
  name: 'Secondary Compact - XL',
  args: {
    type: FinTabType.SECONDARY_COMPACT,
    size: FinSize.XL,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: args,
    template: templateSecondary,
  }),
};

export const Tertiary: Story = {
  args: {
    type: FinTabType.TERTIARY,
    size: FinSize.L,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: args,
    template: `
      <fin-tabs
        #finTabs
        [type]="type"
        [size]="size"
      >
        <fin-tab>
          <ng-template finTabLabel>
            <span finTruncateText class="fin-max-w-[220px]">
              Allianz Bank
            </span>
          </ng-template>

          <ng-template finTabBody>
            Tab content
          </ng-template>
        </fin-tab>

        <fin-tab>
          <ng-template finTabLabel>
            <span finTruncateText class="fin-max-w-[220px]">
              Raiffeisenbank Germany
            </span>
          </ng-template>

          <ng-template finTabBody>
            Tab content
          </ng-template>
        </fin-tab>

        <fin-tab>
          <ng-template finTabLabel>
            <span finTruncateText class="fin-max-w-[220px]">
              Unicredit Bulbank
            </span>
          </ng-template>

          <ng-template finTabBody>
            Tab content
          </ng-template>
        </fin-tab>

        <fin-tab>
          <ng-template finTabLabel>
           <span finTruncateText class="fin-max-w-[220px]">
             Bank of national trust and saving association
           </span>
          </ng-template>

          <ng-template finTabBody>
            Tab content
          </ng-template>
        </fin-tab>

        <fin-tab>
          <ng-template finTabLabel>
            <span finTruncateText class="fin-max-w-[220px]">
              Volksbank (Voksbank Group)
            </span>
          </ng-template>

          <ng-template finTabBody>
            Tab content
          </ng-template>
        </fin-tab>
      </fin-tabs>
    `,
  }),
};
