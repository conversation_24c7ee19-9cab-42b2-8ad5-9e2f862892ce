@if (tabs.length) {
  <mat-tab-group
    #tabsGroup
    class="fin-tabs"
    [ngClass]="['fin-tabs-' + type, 'fin-tabs-' + size]"
    [mat-stretch-tabs]="stretchTabs"
    (focusChange)="onFocusChange($event)"
    (selectedIndexChange)="onSelectedIndexChange($event)"
    (selectedTabChange)="onSelectedTabChange($event)"
    [selectedIndex]="selectedIndex"
  >
    @for (tab of tabs; track tab) {
      <mat-tab
        #matTab
        [disabled]="tab.disabled"
        [labelClass]="tab.isHidden ? 'fin-tab-hidden' : ''"
      >
        <ng-template mat-tab-label>
          <span
            [attr.data-text-label]="tab.labelText"
            class="label-wrapper fin-flex fin-flex-col fin-items-center fin-text-text-body-2-size fin-leading-text-body-2-line-height"
            [ngClass]="{
              'fin-text-color-text-disabled': tab.disabled,
              'fin-text-color-text-primary':
                type === tabType.PRIMARY && !tab.disabled && !matTab.isActive,
              'fin-mt-[0.6rem]': type === tabType.PRIMARY,
            }"
          >
            <span class="fin-label-template">
              <ng-container
                *ngTemplateOutlet="tab.tabLabel.template"
              ></ng-container>
            </span>
          </span>
        </ng-template>

        @if (tab.tabBody) {
          <ng-container *ngTemplateOutlet="tab.tabBody.template"></ng-container>
        }
      </mat-tab>
    }
  </mat-tab-group>
}
