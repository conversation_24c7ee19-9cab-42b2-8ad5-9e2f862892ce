# @fincloud/ui/file-upload

Angular file upload component with drag-and-drop functionality.

## Components

### FinFileUploadComponent (`fin-file-upload`)

File upload component with drag-and-drop support and progress tracking.

**Basic Usage:**

```typescript
import { FinFileUploadModule } from '@fincloud/ui/file-upload';

<fin-file-upload
  [multiple]="true"
  [acceptedFileTypes]="['.pdf', '.doc', '.docx']"
  (filesSelected)="onFilesSelected($event)">
</fin-file-upload>
```

**Key Features:**

- Drag-and-drop file upload
- File type validation
- Upload progress tracking
- Multiple file selection
