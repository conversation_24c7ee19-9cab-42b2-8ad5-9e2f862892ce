# @fincloud/ui/icon

A comprehensive Angular icon component library providing flexible icon display capabilities with support for Material Icons, custom SVG icons, and extensive sizing and styling options.

## Components

### FinIconComponent (`fin-icon`)

The main icon component that provides a graphical representation of objects or actions with support for both Material Icons and custom SVG icons.

**Key Features:**

- Material Icons integration with Google Fonts icon library
- Custom SVG icon support via URL source
- Multiple icon sizes (XS, S, M, L, XL, XXL)
- Outlined and rounded Material Icon variants
- Flexible styling with CSS class support
- Responsive design with em-based sizing
- Accessibility support with proper ARIA attributes
- Built on Angular Material Icons and angular-svg-icon

**Basic Usage:**

```typescript
import { FinIconModule } from '@fincloud/ui/icon';

// Material Icons
<fin-icon
  name="home"
  size="m">
</fin-icon>

// Custom SVG Icons
<fin-icon
  src="assets/icons/custom-icon.svg"
  size="l">
</fin-icon>
```

**Material Icons Usage:**

```typescript
// Standard Material Icons
<fin-icon name="add" size="m"></fin-icon>
<fin-icon name="delete" size="s"></fin-icon>
<fin-icon name="settings" size="l"></fin-icon>

// Outlined vs Rounded variants
<fin-icon name="favorite" [matIconOutlined]="true"></fin-icon>
<fin-icon name="favorite" [matIconOutlined]="false"></fin-icon>
```

**Custom SVG Icons:**

```typescript
// Local SVG files
<fin-icon src="assets/icons/logo.svg" size="xl"></fin-icon>

// External SVG URLs
<fin-icon src="https://example.com/icon.svg" size="m"></fin-icon>
```

**In Buttons and Components:**

```typescript
// In buttons
<button fin-button appearance="primary">
  <fin-icon name="save"></fin-icon>
  Save Document
</button>

// In badges
<fin-badge-icon type="active" size="m">
  <fin-icon name="check" size="s"></fin-icon>
</fin-badge-icon>

// In navigation
<button fin-button-action size="l">
  <fin-icon name="menu"></fin-icon>
</button>
```

## Icon Sizes

The component supports six different sizes with responsive em-based scaling:

- `XS` - Extra small (text-body-3 size) - ~12px
- `S` - Small (text-body-1 size) - ~14px
- `M` - Medium (text-heading-3 size) - ~18px (default)
- `L` - Large (text-heading-2 size) - ~24px
- `XL` - Extra large (~32px)
- `XXL` - Extra extra large (text-display-1 size) - ~48px

## Material Icons

The component integrates with Google's Material Icons font library, providing access to thousands of icons:

**Popular Icon Names:**

- Navigation: `home`, `menu`, `arrow_back`, `arrow_forward`, `close`
- Actions: `add`, `edit`, `delete`, `save`, `search`, `refresh`
- Content: `copy`, `cut`, `paste`, `undo`, `redo`, `print`
- Communication: `email`, `phone`, `chat`, `notifications`
- Files: `folder`, `file_copy`, `download`, `upload`, `attachment`
- User: `person`, `account_circle`, `settings`, `logout`
- Status: `check`, `error`, `warning`, `info`, `help`

**Icon Variants:**

- `matIconOutlined="true"` - Outlined icons (default)
- `matIconOutlined="false"` - Rounded/filled icons

## Installation & Import

```typescript
import { FinIconModule } from '@fincloud/ui/icon';

// Or import individual components
import { FinIconComponent } from '@fincloud/ui/icon';
```

## Advanced Features

### Custom Styling

Icons inherit the current text color and can be styled with CSS:

```typescript
// Custom color
<fin-icon name="star" class="text-yellow-500"></fin-icon>

// Custom styling
<fin-icon name="warning" class="text-red-600 animate-pulse"></fin-icon>
```

### Responsive Sizing

Icons use em-based sizing, making them responsive to their container's font size:

```typescript
// Will scale with parent font size
<div class="text-lg">
  <fin-icon name="info" size="m"></fin-icon>
</div>
```

### Accessibility

- **ARIA Support**: Proper ARIA attributes for screen readers
- **Semantic Meaning**: Icons convey meaning through context
- **Color Independence**: Icons work without relying solely on color
- **Focus Management**: Proper focus indicators when used in interactive elements

### Performance

- **SVG Optimization**: Efficient SVG rendering with angular-svg-icon
- **Font Loading**: Optimized Material Icons font loading
- **Caching**: SVG icons are cached for improved performance
- **Tree Shaking**: Only used icons are included in the bundle

### Integration Examples

```typescript
// In form fields
<fin-input label="Search">
  <fin-icon name="search" finInputPrefix></fin-icon>
</fin-input>

// In dropdowns
<fin-dropdown [options]="options">
  <ng-template finOptionPrefix let-option>
    <fin-icon [name]="option.icon"></fin-icon>
  </ng-template>
</fin-dropdown>

// In tables
<ng-template name="actions" [finRowTemplate]="rows" let-row>
  <button fin-button-action size="s" (click)="edit(row)">
    <fin-icon name="edit"></fin-icon>
  </button>
  <button fin-button-action size="s" (click)="delete(row)">
    <fin-icon name="delete"></fin-icon>
  </button>
</ng-template>
```
