:host {
  @apply fin-flex fin-justify-center fin-items-center fin-p-size-spacing-32;

  //complex
  &.fin-empty-state-complex {
    @apply fin-min-w-[30rem] fin-flex-col fin-gap-size-spacing-32;
    .fin-content {
      @apply fin-flex-col fin-gap-size-spacing-16;
      .fin-title {
        @apply fin-text-color-text-primary fin-text-text-heading-3-size fin-font-text-heading-3-strong-weight;
      }
    }
  }

  //basic
  &.fin-empty-state-basic {
    @apply fin-min-w-[30rem] fin-flex-row fin-gap-size-spacing-16;
    .fin-content {
      @apply fin-flex-row fin-gap-size-spacing-16;
      .fin-title {
        @apply fin-text-color-text-tertiary fin-text-text-body-1-size fin-font-text-body-1-strong-weight;
      }
    }
  }
}
