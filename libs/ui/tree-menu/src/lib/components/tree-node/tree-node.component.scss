:host {
  @apply fin-flex;
  @apply fin-flex-col;
  @apply fin-w-full;

  .fin-tree-node {
    &-content {
      @apply fin-flex;
      @apply fin-flex-row;
      @apply fin-items-center;
      @apply fin-gap-[0.8rem];
      @apply fin-leading-[1.8rem];
      @apply fin-px-[1.6rem];
      @apply fin-py-2;
      @apply fin-min-h-16;
      @apply fin-bg-none;
      @apply fin-transition;
      @apply fin-duration-300;
      @apply fin-ease-in-out;
      background: theme('colors.color-background-light');
    }
    &-node-separator {
      @apply fin-border-b-[0.1rem];
      @apply fin-border-solid;
      @apply fin-border-color-border-default-minimal;
    }

    &-hover-highlight {
      @apply hover:fin-bg-color-hover-tertiary;
    }
  }

  &.fin-tree-node-primary {
    @apply fin-text-body-3-strong;
  }

  &.fin-tree-node-secondary {
    @apply fin-text-body-3-moderate;
    @apply fin-text-color-text-secondary;
  }
}

// ng-deep is need because we have to apply this styles to projected templates
::ng-deep .fin-tree-node:hover {
  .fin-tree-node-hover-hidden {
    display: none !important;
  }
  .fin-tree-node-hover-block {
    display: block !important;
  }
  .fin-tree-node-hover-flex {
    display: flex !important;
  }
}
