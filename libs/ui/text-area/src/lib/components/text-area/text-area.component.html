<label
  class="fin-label fin-flex fin-justify-between fin-items-center"
  [ngClass]="{
    'fin-text-color-text-disabled': control.disabled,
    'fin-text-color-text-primary': readonly,
  }"
  [for]="control"
>
  @if (label) {
    <span class="fin-w-full" finTruncateText> {{ label }} </span>
  } @else {
    <ng-content select="[finInputLabel]"></ng-content>
  }
</label>
<mat-form-field
  class="fin-field fin-flex fin-flex-col fin-flex-grow fin-field-size-{{
    size
  }}"
  [subscriptSizing]="dynamicErrorSpace"
  [class.fin-field-readonly]="readonly"
  [class.fin-field-auto-resize]="autoResize"
  [ngClass]="{
    'fin-field-error':
      (externalFieldMessages?.type || (getMessage$ | async)?.type) === 'error',
    'fin-field-warning':
      (externalFieldMessages?.type || (getMessage$ | async)?.type) ===
      'warning',
    'fin-field-success':
      (externalFieldMessages?.type || (getMessage$ | async)?.type) ===
      'success',
  }"
>
  <fin-scrollbar
    [class.fin-resize-y]="autoResize"
    [class.fin-max-h-[488px]]="autoResize"
    [class.fin-min-h-[128px]]="autoResize"
    [class.fin-pb-[8px]]="autoResize"
  >
    <textarea
      [attr.maxlength]="maxLength ? maxLength : null"
      [maxLength]="maxLength"
      #textarea
      autosize
      [minRows]="rows"
      matInput
      type="text"
      [formControl]="control"
      (blur)="blur($event)"
      (keydown.enter)="keydown($event)"
      [placeholder]="readonly ? '' : placeholder"
      [readonly]="readonly"
      class="fin-resize-none"
    ></textarea>
  </fin-scrollbar>

  @if (aiEnabled) {
    <fin-ai-suggestion
      class="fin-whitespace-normal"
      [aiEnabled]="aiEnabled"
      [formControl]="control"
      (aiSuggestionReady)="onAiSuggestionReady()"
    >
    </fin-ai-suggestion>
  }

  <div
    #suffix
    matSuffix
    class="fin-suffix fin-flex fin-items-center fin-justify-center"
    [class.fin-pointer-events-none]="control.disabled"
  >
    <ng-content select="[finInputSuffix]"></ng-content>
  </div>
  <mat-hint class="fin-w-full">
    @if (
      control.valid && control.touched && (getMessage$ | async);
      as message
    ) {
      @if (message.type === 'success') {
        <ng-container *ngTemplateOutlet="message.template"></ng-container>
      }
    } @else {
      <div class="fin-hint">
        <ng-content select="[finInputHint]"></ng-content>
      </div>
    }
  </mat-hint>
  @if ((getMessage$ | async)?.template; as messageTemplate) {
    <mat-error>
      <div class="fin-inline-block fin-w-full">
        <ng-container *ngTemplateOutlet="messageTemplate"></ng-container>
      </div>
    </mat-error>
  }
</mat-form-field>
