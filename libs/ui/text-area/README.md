# @fincloud/ui/text-area

A comprehensive Angular text area component library providing multi-line text input fields with advanced features like auto-resize, character limits, AI suggestions, and custom scrollbar integration.

## Components

### FinTextAreaComponent (`fin-text-area`)

The main text area component that provides a multi-line input field for entering text with extensive customization and form integration capabilities.

**Key Features:**

- Multi-line text input with auto-resize functionality
- Character limit enforcement with visual feedback
- AI suggestion integration
- Custom scrollbar with resize controls
- Multiple sizes (M, L)
- Readonly mode support
- Reactive forms integration with ControlValueAccessor
- Built-in validation state display
- Dynamic error space management
- Prefix and suffix content projection
- Built on Angular Material Form Field

**Basic Usage:**

```typescript
import { FinTextAreaModule } from '@fincloud/ui/text-area';

// In component
textControl = new FormControl('');

// In template
<fin-text-area
  label="Description"
  placeholder="Enter your description here..."
  [formControl]="textControl"
  [rows]="5"
  [maxLength]="500"
  size="m">
</fin-text-area>
```

**Auto-Resize Text Area:**

```typescript
<fin-text-area
  label="Auto-resizing Comment"
  placeholder="Type your comment..."
  [formControl]="commentControl"
  [autoResize]="true"
  [rows]="3"
  [maxLength]="1000">
</fin-text-area>
```

**Text Area with AI Suggestions:**

```typescript
<fin-text-area
  label="AI-Enhanced Description"
  placeholder="Start typing for AI suggestions..."
  [formControl]="aiTextControl"
  [aiEnabled]="true"
  [rows]="4"
  (aiSuggestionReady)="onAiSuggestionReady()">
</fin-text-area>
```

**Readonly Text Area:**

```typescript
<fin-text-area
  label="Read-only Content"
  [formControl]="readonlyControl"
  [readonly]="true"
  [rows]="6">
</fin-text-area>
```

**Text Area with Custom Validation:**

```typescript
// In component
descriptionControl = new FormControl('', [
  Validators.required,
  Validators.minLength(10),
  Validators.maxLength(500)
]);

// In template
<fin-text-area
  label="Product Description"
  placeholder="Describe your product (minimum 10 characters)"
  [formControl]="descriptionControl"
  [maxLength]="500"
  [rows]="4"
  size="l">
</fin-text-area>
```

## Configuration Options

### Size Options

- `FinSize.M` - Medium text area (default)
- `FinSize.L` - Large text area

### Text Area Properties

- `label` - Label text displayed above the text area
- `placeholder` - Placeholder text shown when empty
- `rows` - Number of visible text rows (default: 5)
- `maxLength` - Maximum number of characters allowed
- `autoResize` - Enable automatic height adjustment based on content
- `readonly` - Make the text area read-only
- `aiEnabled` - Enable AI suggestion functionality

### Form Integration

- `formControl` - Reactive form control binding
- `dynamicErrorSpace` - Error space management configuration
- `externalFieldMessages` - External validation message configuration

## Installation & Import

```typescript
import { FinTextAreaModule } from '@fincloud/ui/text-area';

// Or import individual components
import { FinTextAreaComponent } from '@fincloud/ui/text-area';
```

## Advanced Features

### Auto-Resize Functionality

The text area can automatically adjust its height based on content:

```typescript
<fin-text-area
  [autoResize]="true"
  [rows]="3"  // Minimum rows
  label="Expanding Text Area">
  <!-- Automatically grows from 3 rows minimum -->
  <!-- Maximum height is 488px with scrolling -->
  <!-- Minimum height is 128px -->
</fin-text-area>
```

### Character Limit Management

Visual feedback for character limits with real-time counting:

```typescript
<fin-text-area
  label="Limited Text"
  [maxLength]="200"
  [formControl]="limitedControl">
  <!-- Character count automatically displayed -->
  <!-- Visual feedback when approaching limit -->
</fin-text-area>
```

### AI Integration

Enable AI-powered suggestions for enhanced user experience:

```typescript
<fin-text-area
  [aiEnabled]="true"
  (aiSuggestionReady)="handleAiSuggestion()"
  label="AI-Enhanced Input">
  <!-- AI suggestions appear as user types -->
  <!-- Configurable AI behavior and triggers -->
</fin-text-area>
```

### Custom Scrollbar

Built-in custom scrollbar for consistent styling:

```typescript
<fin-text-area
  [autoResize]="false"
  [rows]="10"
  label="Scrollable Content">
  <!-- Custom scrollbar styling -->
  <!-- Consistent with application theme -->
  <!-- Touch-friendly on mobile devices -->
</fin-text-area>
```

## Event Handling

### Available Events

- `blur` - Emitted when the text area loses focus
- `keydown` - Emitted on keydown events (including Enter key handling)
- `aiSuggestionReady` - Emitted when AI suggestions are available

### Event Usage

```typescript
<fin-text-area
  (blur)="onTextAreaBlur($event)"
  (keydown.enter)="onEnterKey($event)"
  (aiSuggestionReady)="onAiReady()">
</fin-text-area>
```

## Form Integration Examples

### Reactive Forms

```typescript
// In component
commentForm = this.fb.group({
  title: ['', [Validators.required, Validators.maxLength(100)]],
  description: ['', [Validators.required, Validators.minLength(20), Validators.maxLength(1000)]],
  notes: ['']
});

// In template
<form [formGroup]="commentForm">
  <fin-input
    label="Title"
    formControlName="title"
    [maxLength]="100">
  </fin-input>

  <fin-text-area
    label="Description"
    formControlName="description"
    [maxLength]="1000"
    [rows]="4"
    placeholder="Provide a detailed description...">
  </fin-text-area>

  <fin-text-area
    label="Additional Notes"
    formControlName="notes"
    [rows]="3"
    [autoResize]="true"
    placeholder="Optional notes...">
  </fin-text-area>
</form>
```

### Validation Integration

```typescript
// Custom validation
<fin-text-area
  label="Validated Text"
  [formControl]="validatedControl"
  [maxLength]="500">
  <!-- Validation errors automatically displayed -->
  <!-- Error styling applied based on form state -->
</fin-text-area>
```

## Advanced Features

### Performance

- **Change Detection**: OnPush strategy for optimal performance
- **Auto-resize Optimization**: Efficient height calculation and updates
- **Memory Management**: Proper cleanup and no memory leaks
- **Scroll Optimization**: Smooth scrolling with custom scrollbar

### Accessibility

- **ARIA Support**: Full ARIA attributes for screen readers
- **Keyboard Navigation**: Standard keyboard interaction patterns
- **Focus Management**: Proper focus indicators and management
- **Label Association**: Proper label-input association for assistive technologies

### Responsive Design

- **Flexible Sizing**: Adapts to container width
- **Mobile Optimization**: Touch-friendly interaction on mobile devices
- **Auto-resize**: Responsive height adjustment based on content
- **Consistent Styling**: Maintains appearance across different screen sizes

### Integration Features

- **Scrollbar Integration**: Custom scrollbar component for consistent styling
- **AI Integration**: Seamless AI suggestion functionality
- **Form Field Integration**: Full Angular Material form field integration
- **Validation Integration**: Complete validation state management and display
