import { Meta, StoryObj } from '@storybook/angular';
import { FinScrollbarComponent } from './scrollbar.component';

const meta: Meta<FinScrollbarComponent> = {
  title: 'Components/Scrollbar',
  component: FinScrollbarComponent,
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinScrollbarModule } from "@fincloud/ui/scrollbar"`' +
          '<br><br>' +
          'This component wraps [NgxScrollbar](https://github.com/MurhafSousli/ngx-scrollbar/?tab=readme-ov-file) and exposes part of its API.',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=12064-43093&t=9VqDNc2Hjlx4Ntqo-4',
    },
  },
};

export default meta;
type Story = StoryObj<FinScrollbarComponent>;

export const BasicScroll: Story = {
  argTypes: {
    enableInfinityScroll: {
      description:
        '<span style="color:red">**Deprecated** <br> Anyone who needs to listen for events like "reachedBottom" can do so directly without setting **enableInfinityScroll** to **true**.</span>',
    },
    update: {
      type: 'function',
    },
    afterUpdate: {
      type: 'function',
    },
    afterInit: {
      type: 'function',
    },
    reachedBottom: {
      type: 'function',
    },
    reachedTop: {
      type: 'function',
    },
    scrollTo: {
      type: 'function',
    },
    scrollToElement: {
      type: 'function',
    },
    alwaysVisible: {
      control: 'boolean',
    },
    smoothResize: {
      control: 'boolean',
    },
    smoothResizeDuration: {
      control: 'number',
    },
  },

  loaders: [
    async () => ({
      items: await (
        await fetch('/assets/storybook/mocks/scroll-content-part-1.json')
      ).json(),
    }),
  ],
  render: (args, { loaded }) => {
    return {
      props: {
        ...args,
        rowsToShow: loaded['items'],
      },
      template: `
        <fin-scrollbar 
        [alwaysVisible]="alwaysVisible"
        [smoothResize]="smoothResize"
        [smoothResizeDuration]="smoothResizeDuration"
          class="fin-h-[30rem]"
        >
          <div *ngFor="let log of rowsToShow" class="fin-p-4 fin-border-b-2">
            <div><strong>IP Address:</strong> {{ log.ipAddress }}</div>
            <div><strong>Device:</strong> {{ log.device }}</div>
            <div><strong>Operation:</strong> {{ log.operation }}</div>
            <div><strong>Status:</strong> {{ log.status }}</div>
          </div>
        </fin-scrollbar>
      `,
    };
  },
};

// export const InfiniteScroll: Story = {
//   argTypes: {
//     ...argTypes,
//   },
//   args: {
//     ...args,
//   },
//   loaders: [
//     async () => ({
//       items: await (
//         await fetch('/assets/storybook/mocks/scroll-content-part-1.json')
//       ).json(),
//     }),
//   ],
//   render: (args, { loaded }) => {
//     moreItems = [...loaded['items']];
//     return {
//       props: {
//         ...args,
//         loadLogs,
//         rowsToShow: moreItems,
//       },
//       template: `
//         <fin-scrollbar
//           class="fin-h-[30rem]"
//           enableInfinityScroll
//           (reachedBottom)="loadLogs()"
//         >
//           <div *ngFor="let log of rowsToShow" class="fin-p-4 fin-border-b-2">
//             <div><strong>IP Address:</strong> {{ log.ipAddress }}</div>
//             <div><strong>Device:</strong> {{ log.device }}</div>
//             <div><strong>Operation:</strong> {{ log.operation }}</div>
//             <div><strong>Status:</strong> {{ log.status }}</div>
//           </div>
//         </fin-scrollbar>
//       `,
//     };
//   },
// };
