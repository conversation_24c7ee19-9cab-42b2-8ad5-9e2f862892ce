import { CdkScrollableModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  ViewChild,
  booleanAttribute,
  numberAttribute,
} from '@angular/core';
import { FinObserversModule } from '@fincloud/ui/observers';
import {
  NgScrollbar,
  NgScrollbarModule,
  ScrollbarUpdateReason,
} from 'ngx-scrollbar';
import { NgScrollReached } from 'ngx-scrollbar/reached-event';
import { Observable, startWith } from 'rxjs';
import { FinScrollbarUpdateReason } from '../../enums/fin-scrollbar-update-reason';
import { FinSmoothScrollToOptions } from '../../models/fin-infinite-scroll-event';
import { FinSmoothScrollElement } from '../../models/fin-smooth-scroll-element';
import { FinSmoothScrollToElementOptions } from '../../models/fin-smooth-scroll-to-element-options';
import { FinScrollbarService } from '../../service/fin-scrollbar.service';

/**
 * A component that allows users to scroll through content.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-scrollbar--docs Storybook Reference}
 */
@Component({
  selector: 'fin-scrollbar',
  standalone: true,
  imports: [
    CommonModule,
    NgScrollbarModule,
    NgScrollReached,
    CdkScrollableModule, // It is needed for the Angular scroll strategy in Mat Select, Mat Autocomplete, etc.
    FinObserversModule,
  ],
  templateUrl: './scrollbar.component.html',
  styleUrl: './scrollbar.component.scss',
  host: {
    '[class]': 'containerClasses',
    '[style]': 'containerStyles',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  exportAs: 'finScrollbar',
})
export class FinScrollbarComponent {
  /** Reached top offset in pixels. */
  @Input() reachedTopOffset = 50;

  /** Reached bottom offset in pixels. */
  @Input() reachedBottomOffset = 50;

  /** @deprecated Previously used as [disableReached]="!enableInfinityScroll", which only enabled the "reached" events.
   * This is not necessary. 
   * Anyone who needs to listen for events like "reachedBottom" can do so directly without setting `enableInfinityScroll` to `true`. */
  @Input({ transform: booleanAttribute }) enableInfinityScroll = false;

  /** Make the scrollbar always visible. */
  @Input({ transform: booleanAttribute }) alwaysVisible = false;

  /** Disables the scrollbar, default false. */
  @Input({ transform: booleanAttribute }) disabled = false;

  /** Enables or disables the height transition animation for the scrollbar. */
  @Input({ transform: booleanAttribute }) smoothResize = false;

  /**
   * Enables smooth resizing animation for the scrollbar duration.
   * Only works in combination with `smoothResize`.
   */
  @Input({ transform: numberAttribute }) smoothResizeDuration = 400;

  /** A stream that emits when scroll has reached the top. */
  @Output() reachedTop = new EventEmitter<void>();

  /** A stream that emits when scroll has reached the bottom. */
  @Output() reachedBottom = new EventEmitter<void>();

  /** Output that emits after the scrollbar component is initialized. */
  @Output() afterInit = new EventEmitter<void>();

  /** Output that emits after the scrollbar component is updated. */
  @Output() afterUpdate = new EventEmitter<void>();

  @ViewChild(NgScrollbar) private panel!: NgScrollbar;

  protected getIsDisabled$: Observable<boolean> =
    this.finScrollbarService.disabled$.pipe(startWith(this.disabled));

  protected get containerClasses(): string {
    const classes = ['fin-scrollbar'];
    if (this.smoothResize) {
      classes.push('smooth-resize');
    }
    return classes.join(' ');
  }

  protected get containerStyles(): string {
    const styles = [];
    if (this.smoothResize) {
      styles.push('transition-duration: ' + this.smoothResizeDuration + 'ms');
    }
    return styles.join(';');
  }

  constructor(private finScrollbarService: FinScrollbarService) {}

  protected reachTop() {
    this.reachedTop.emit();
  }
  protected reachBottom() {
    this.reachedBottom.emit();
  }

  protected onAfterInit() {
    this.afterInit.emit();
  }

  protected onAfterUpdate() {
    this.afterUpdate.emit();
  }

  protected onResize() {
    this.update(FinScrollbarUpdateReason.RESIZED);
  }

  /** Trigger a re-calculation to update the scrollbar. */
  update(reason?: FinScrollbarUpdateReason) {
    let selectedReason;
    if (reason === FinScrollbarUpdateReason.AFTER_INIT) {
      selectedReason = ScrollbarUpdateReason.AfterInit;
    } else if (reason === FinScrollbarUpdateReason.RESIZED) {
      selectedReason = ScrollbarUpdateReason.Resized;
    }

    this.panel.update(selectedReason);
  }

  /** Scroll function that returns a promise that resolves when scroll is reached. */
  scrollTo(options: FinSmoothScrollToOptions): Promise<void> {
    return this.panel.scrollTo(options);
  }

  /** Scroll function that returns a promise that resolves when scroll is reached. */
  scrollToElement(
    target: FinSmoothScrollElement,
    options: FinSmoothScrollToElementOptions,
  ): Promise<void> {
    return this.panel.scrollToElement(target, options);
  }
}
