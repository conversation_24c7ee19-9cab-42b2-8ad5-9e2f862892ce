# @fincloud/ui/expansion-panel

Angular expansion panel component for collapsible content sections.

## Components

### FinExpansionPanelComponent (`fin-expansion-panel`)

Collapsible panel with header and expandable content area.

**Basic Usage:**

```typescript
import { FinExpansionPanelModule } from '@fincloud/ui/expansion-panel';

<fin-expansion-panel>
  <fin-expansion-panel-header>
    Panel Title
  </fin-expansion-panel-header>
  <fin-expansion-panel-content>
    Expandable content goes here
  </fin-expansion-panel-content>
</fin-expansion-panel>
```

**Key Features:**

- Collapsible content sections
- Custom header and content
- Smooth expand/collapse animations
- Accordion support
