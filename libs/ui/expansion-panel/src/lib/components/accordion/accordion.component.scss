$color-surface-primary: theme('colors.color-surface-primary');
$color-border-default-minimal: theme('colors.color-border-default-minimal');
$color-text-primary: theme('colors.color-text-primary');
$color-text-secondary: theme('colors.color-text-secondary');
$color-hover-tertiary: theme('colors.color-hover-tertiary');
:host {
  display: block;
}
::ng-deep {
  // base
  .fin-accordion {
    --mat-expansion-header-hover-state-layer-color: #{$color-hover-tertiary};

    &.fin-accordion-toggle {
      &-left {
        > .fin-expansion-panel {
          .mat-expansion-indicator {
            &::after {
              transform: rotate(135deg);
            }
          }
        }
      }
      &-right {
        > .fin-expansion-panel {
          .mat-expansion-indicator {
            &::after {
              transform: rotate(-45deg);
            }
          }
        }
      }
      &-right,
      &-left {
        > .fin-expansion-panel {
          .mat-expansion-indicator {
            &::after {
              margin-bottom: 0.5rem;
            }
          }
          .mat-expanded {
            .mat-expanded {
              .mat-expansion-indicator {
                &::after {
                  transform: rotate(-135deg);
                  margin-top: 0.5rem;
                }
              }
            }
          }
        }
      }
    }

    .mat-expansion-panel {
      border-radius: 0 !important;
      background-color: $color-surface-primary;

      &:not([class*='mat-elevation-z']) {
        box-shadow: none;
      }
      &-header {
        &-title,
        &-description,
        .fin-summary {
          @apply fin-text-body-3-moderate;
          color: $color-text-primary;
          margin-right: 0;
        }
        &.mat-expansion-toggle-indicator-before {
          .mat-expansion-indicator {
            @apply fin-mr-size-spacing-12 #{!important};
          }
        }
        .mat-expansion-indicator {
          &::after {
            border-color: $color-text-primary;
          }
        }
      }
      &-content {
        color: $color-text-secondary;
      }
      > .mat-expansion-panel {
        &-content {
          .fin-panel-content {
            @apply fin-py-[1.2rem];
          }
        }
      }
      .mat-expansion-panel-body {
        padding: 0;
      }
      .mat-expansion-indicator {
        &::after {
          margin-bottom: 0.5rem;
          @apply fin-mx-[0.2rem];
        }
      }
    }

    // set borders
    > .fin-expansion-panel {
      > .mat-expansion-panel {
        > .mat-expansion-panel-header {
          border-bottom: 1px solid $color-border-default-minimal;
        }

        &.mat-expanded {
          > .mat-expansion-panel-content {
            border-bottom: 1px solid $color-border-default-minimal;
          }

          &:has(.fin-accordion) {
            > .mat-expansion-panel-content {
              border-bottom: none;
            }
          }
        }
      }
    }

    // size L
    &.fin-accordion-l {
      .mat-expansion-panel {
        &-header {
          @apply fin-px-[2.4rem];
          height: 6.4rem;

          .mat-content {
            @apply fin-gap-[2.4rem];
          }

          &-title {
            @apply fin-text-body-1-moderate;
          }
          &-description,
          .fin-summary {
            @apply fin-text-body-2-moderate;
          }
        }
        &-content {
          @apply fin-text-body-1-moderate;

          .fin-panel-content {
            @apply fin-px-[2.4rem];
          }
        }
      }
    }

    // size M
    &.fin-accordion-m {
      .mat-expansion-panel {
        &-header {
          @apply fin-px-[2.4rem];
          height: 4.8rem;

          .mat-content {
            @apply fin-gap-[1.6rem];
          }

          &-title {
            @apply fin-text-body-1-strong;
          }

          &-description,
          .fin-summary {
            @apply fin-text-body-1-moderate;
          }
        }
        &-content {
          @apply fin-text-body-1-moderate;

          .fin-panel-content {
            @apply fin-px-[2.4rem];
          }
        }
      }
    }

    // size S
    &.fin-accordion-s {
      .mat-expansion-panel {
        &-header {
          @apply fin-px-[1.6rem];
          height: 4rem;

          &.mat-expansion-toggle-indicator-before {
            .mat-content.mat-content-hide-toggle {
              @apply fin-ml-size-spacing-20;
            }
            .mat-expansion-indicator {
              @apply fin-mr-size-spacing-8 #{!important};
            }
          }
          .mat-content {
            @apply fin-gap-[0.8rem];
          }

          &-title {
            @apply fin-text-body-3-strong;
          }

          &-description,
          .fin-summary {
            @apply fin-text-body-3-moderate;
          }
        }
        &-content {
          @apply fin-text-body-3-moderate;

          .fin-panel-content {
            @apply fin-px-[1.6rem];
          }
        }
      }
    }
  }

  // accordion Light
  .fin-accordion-light {
    > .fin-expansion-panel {
      > .mat-expansion-panel {
        > .mat-expansion-panel-content
          > .mat-expansion-panel-body
          > .fin-panel-content {
          padding: 0;
        }
      }
    }
  }
}
