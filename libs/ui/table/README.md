# @fincloud/ui/table

A comprehensive Angular table component library providing advanced data display capabilities with sorting, grouping, expandable rows, custom templates, and responsive design features.

## Components

### FinTableComponent (`fin-table`)

The main table component for displaying data in rows and columns with extensive customization options and advanced features.

**Key Features:**

- Flexible column configuration with multiple modes (flex, standard, force)
- Built-in sorting with custom and default sort implementations
- Row grouping and expandable row details
- Custom templates for rows, headers, and group headers
- Responsive design with automatic column hiding
- Multiple styling options (borders, spacing, radius)
- No data state handling
- Accessibility support with proper ARIA attributes
- Built on Angular Material Table

**Basic Usage:**

```typescript
import { FinTableModule } from '@fincloud/ui/table';

// In component
interface TableRow {
  id: number;
  name: string;
  email: string;
  status: string;
}

rows: TableRow[] = [
  { id: 1, name: '<PERSON>', email: '<EMAIL>', status: 'Active' },
  { id: 2, name: '<PERSON>', email: '<EMAIL>', status: 'Inactive' }
];

columns: FinTableColumn[] = [
  {
    name: 'Name',
    prop: 'name',
    templateName: 'nameTemplate',
    isSortable: true,
    flexGrow: 2
  },
  {
    name: 'Email',
    prop: 'email',
    templateName: 'emailTemplate',
    isSortable: true
  },
  {
    name: 'Status',
    prop: 'status',
    templateName: 'statusTemplate',
    isSortable: false
  }
];

// In template
<fin-table
  [rows]="rows"
  [columns]="columns"
  [columnMode]="columnMode.FLEX"
  [headerHeight]="44"
  [rowHeight]="64"
  hasRowSpacing
  hasBorderRadius>

  <ng-template name="nameTemplate" [finRowTemplate]="rows" let-row>
    <div class="fin-flex fin-items-center">
      <fin-avatar-default
        [firstName]="row.firstName"
        [lastName]="row.lastName"
        size="s">
      </fin-avatar-default>
      <span class="fin-ms-2">{{ row.name }}</span>
    </div>
  </ng-template>

  <ng-template name="emailTemplate" [finRowTemplate]="rows" let-row>
    <a [href]="'mailto:' + row.email">{{ row.email }}</a>
  </ng-template>

  <ng-template name="statusTemplate" [finRowTemplate]="rows" let-row>
    <fin-badge-status [text]="row.status" [type]="row.status"></fin-badge-status>
  </ng-template>
</fin-table>
```

## Template Directives

### FinRowTemplateDirective (`finRowTemplate`)

Defines custom templates for table row cells with access to row data.

**Usage:**

```typescript
<ng-template name="customCell" [finRowTemplate]="rows" let-row>
  <div class="custom-cell-content">
    {{ row.customProperty }}
  </div>
</ng-template>
```

### FinHeaderTemplateDirective (`finHeaderTemplate`)

Defines custom templates for table header cells.

**Usage:**

```typescript
<ng-template name="customHeader" [finHeaderTemplate]="columns" let-column>
  <div class="fin-flex fin-items-center">
    {{ column.name }}
    <fin-icon name="info" size="s" class="fin-ms-1"></fin-icon>
  </div>
</ng-template>
```

### FinRowDetailsTemplateDirective (`finRowDetailsTemplate`)

Defines templates for expandable row details content.

**Usage:**

```typescript
<ng-template [finRowDetailsTemplate]="rows" let-row>
  <div class="fin-p-4">
    <h4>Additional Details</h4>
    <p>{{ row.detailedDescription }}</p>
  </div>
</ng-template>
```

### FinRowGroupTemplateDirective (`finRowGroupTemplate`)

Defines templates for row group headers when using row grouping.

**Usage:**

```typescript
<ng-template [finRowGroupTemplate]="rows" let-group>
  <div class="fin-font-semibold">
    {{ group.key }} ({{ group.items.length }} items)
  </div>
</ng-template>
```

## Models & Interfaces

### FinTableColumn

Configuration interface for table columns.

```typescript
interface FinTableColumn {
  prop: string; // Property name in row data
  name: string; // Display name for column header
  templateName: string; // Name of the template to use for cells
  isSortable: boolean; // Whether column supports sorting
  headerTemplate?: string; // Optional custom header template name
  flexGrow?: number; // Flex grow value for flex column mode
  width?: number; // Fixed width for standard column mode
  hideThreshold?: number; // Screen width threshold for auto-hiding
}
```

### FinTableSort

Interface for table sorting configuration.

```typescript
interface FinTableSort {
  prop: string; // Property to sort by
  dir: 'asc' | 'desc'; // Sort direction
}
```

### FinTableColumnMode

Enumeration of available column modes.

```typescript
enum FinTableColumnMode {
  STANDARD = 'standard', // Fixed width columns (default: 100px)
  FLEX = 'flex', // Flexible columns with flexGrow
  FORCE = 'force', // Automatically aligned columns
}
```

## Installation & Import

```typescript
import { FinTableModule } from '@fincloud/ui/table';

// Or import individual components and directives
import { FinTableComponent, FinRowTemplateDirective, FinHeaderTemplateDirective, FinRowDetailsTemplateDirective, FinRowGroupTemplateDirective, FinTableColumn, FinTableSort, FinTableColumnMode } from '@fincloud/ui/table';
```

## Advanced Features

### Sorting

- **Built-in Sorting**: Use `useDefaultSort="true"` for automatic client-side sorting
- **Custom Sorting**: Handle `(sortChange)` event for server-side or custom sorting logic
- **Initial Sort**: Set default sort with `[initialSort]` property
- **Column Configuration**: Control sortability per column with `isSortable` property

### Row Grouping

Group table rows by a specific property using `groupRowsBy` attribute:

```typescript
<fin-table
  [rows]="rows"
  [columns]="columns"
  groupRowsBy="category">

  <ng-template [finRowGroupTemplate]="rows" let-group>
    <strong>{{ group.key }}</strong> ({{ group.items.length }} items)
  </ng-template>
</fin-table>
```

### Expandable Rows

Enable row expansion for detailed views:

```typescript
<fin-table
  [rows]="rows"
  [columns]="columns"
  [canExpand]="true"
  multiExpandDetails>

  <ng-template [finRowDetailsTemplate]="rows" let-row>
    <div class="expanded-content">
      <!-- Detailed row content -->
    </div>
  </ng-template>
</fin-table>
```

### Responsive Design

- **Auto-hide Columns**: Use `autoHideColumns="true"` with `hideThreshold` in column config
- **Manual Column Control**: Use `[columnsToHide]` array for programmatic column hiding
- **Flexible Layouts**: Choose appropriate `columnMode` for different screen sizes

### Styling Options

- `hasRowBorder` - Add borders between rows
- `hasRowPartialBorder` - Add partial borders for subtle separation
- `hasRowSpacing` - Add spacing between rows
- `hasBorderRadius` - Add rounded corners to the table
- `headerClasses` - Custom CSS classes for header styling

### No Data State

Customize the empty state when no data is available:

```typescript
<fin-table [rows]="[]" [columns]="columns">
  <ng-template #finTableNoDataTemplate>
    <div class="fin-text-center fin-p-8">
      <fin-icon name="inbox" size="xl"></fin-icon>
      <p>No data available</p>
    </div>
  </ng-template>
</fin-table>
```

### Performance Features

- **Change Detection**: OnPush strategy for optimal performance
- **Virtual Scrolling**: Built-in support for large datasets
- **Template Caching**: Efficient template reuse and rendering
- **Lazy Loading**: Compatible with pagination and infinite scroll patterns
