# @fincloud/ui/charts

A comprehensive Angular charts component library providing data visualization components built on Chart.js with support for bar charts, doughnut charts, area charts, and advanced customization options.

## Components

### FinBarChartComponent (`fin-bar-chart`)

A chart component that displays data with rectangular bars proportional to the values they represent, ideal for comparing quantities across categories.

**Key Features:**

- Horizontal and vertical bar orientations
- Stacked and grouped bar configurations
- Automatic color generation
- Grid line customization
- Custom value prefixes and suffixes
- Responsive design with Chart.js integration
- Tooltip customization
- Legend management

**Basic Usage:**

```typescript
import { FinChartsModule } from '@fincloud/ui/charts';

// In component
interface BarData {
  label: string;
  data: number[];
  backgroundColor?: string;
}

chartLabels = ['Q1', 'Q2', 'Q3', 'Q4'];
chartBars: BarData[] = [
  {
    label: 'Revenue',
    data: [100, 150, 200, 180],
    backgroundColor: '#3B82F6'
  },
  {
    label: 'Profit',
    data: [30, 45, 60, 54],
    backgroundColor: '#10B981'
  }
];

// In template
<fin-bar-chart
  [labels]="chartLabels"
  [bars]="chartBars"
  [showGrid]="true"
  valuePrefix="$"
  valueSuffix="K">
</fin-bar-chart>
```

**Stacked Bar Chart:**

```typescript
<fin-bar-chart
  [labels]="monthLabels"
  [bars]="stackedData"
  [areBarsStacked]="true"
  [autoColors]="true"
  [showGrid]="false">
</fin-bar-chart>
```

### FinDoughnutChartComponent (`fin-doughnut-chart`)

A circular chart component that displays data as segments of a doughnut, perfect for showing proportional data and percentages.

**Key Features:**

- Customizable doughnut size and cutout
- Multiple size variants (S, M, L)
- Automatic color generation
- Custom value formatting
- Tooltip configuration
- Total value calculation
- Responsive design
- Empty state handling

**Basic Usage:**

```typescript
// In component
chartLabels = ['Desktop', 'Mobile', 'Tablet'];
chartValues = [45, 35, 20];
chartColors = ['#3B82F6', '#10B981', '#F59E0B'];

// In template
<fin-doughnut-chart
  [labels]="chartLabels"
  [values]="chartValues"
  [colors]="chartColors"
  [size]="finSize.M"
  valuePrefix=""
  valueSuffix="%"
  [enabledTooltip]="true">
</fin-doughnut-chart>
```

**Auto-colored Doughnut Chart:**

```typescript
<fin-doughnut-chart
  [labels]="categoryLabels"
  [values]="categoryValues"
  [autoColors]="true"
  [size]="finSize.L"
  [totalChartValue]="100">
</fin-doughnut-chart>
```

### FinAreaChartComponent (`fin-area-chart`)

A chart component that displays data as filled areas, ideal for showing trends over time and cumulative values.

**Key Features:**

- Filled area visualization
- Multiple data series support
- Gradient fill options
- Time-series data support
- Smooth curve interpolation
- Grid and axis customization
- Responsive design
- Interactive tooltips

**Basic Usage:**

```typescript
// In component
timeLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
areaData = [
  {
    label: 'Sales',
    data: [12, 19, 15, 25, 22, 30],
    backgroundColor: 'rgba(59, 130, 246, 0.3)',
    borderColor: '#3B82F6'
  }
];

// In template
<fin-area-chart
  [labels]="timeLabels"
  [datasets]="areaData"
  [showGrid]="true"
  valuePrefix="$"
  valueSuffix="K">
</fin-area-chart>
```

## Models & Interfaces

### FinBarChart

Interface for bar chart data configuration.

```typescript
interface FinBarChart {
  label: string; // Dataset label
  data: number[]; // Data values for each bar
  backgroundColor?: string; // Bar color (optional if autoColors is used)
}
```

### Chart Configuration Options

Common configuration options across chart components:

- `labels: string[]` - Labels for chart categories/axes
- `autoColors: boolean` - Automatically generate colors for data series
- `showGrid: boolean` - Display grid lines on the chart
- `valuePrefix: string` - Prefix for displayed values (e.g., "$")
- `valueSuffix: string` - Suffix for displayed values (e.g., "%", "K")
- `enabledTooltip: boolean` - Enable/disable interactive tooltips

## Installation & Import

```typescript
import { FinChartsModule } from '@fincloud/ui/charts';

// Or import individual components
import { FinBarChartComponent, FinDoughnutChartComponent, FinAreaChartComponent } from '@fincloud/ui/charts';
```

## Chart Types & Use Cases

### Bar Charts

Best for:

- Comparing quantities across categories
- Showing changes over time
- Displaying grouped or stacked data
- Ranking and comparison analysis

### Doughnut Charts

Best for:

- Showing proportional data
- Displaying percentages and parts of a whole
- Creating compact data visualizations
- Dashboard summary widgets

### Area Charts

Best for:

- Showing trends over time
- Displaying cumulative values
- Visualizing continuous data
- Highlighting data volume and flow

## Advanced Configuration

### Custom Colors

```typescript
// Manual color specification
chartColors = [
  '#3B82F6', // Blue
  '#10B981', // Green
  '#F59E0B', // Yellow
  '#EF4444', // Red
  '#8B5CF6'  // Purple
];

<fin-doughnut-chart
  [colors]="chartColors"
  [autoColors]="false">
</fin-doughnut-chart>
```

### Responsive Design

```typescript
// Charts automatically adapt to container size
<div class="chart-container" style="width: 100%; height: 400px;">
  <fin-bar-chart
    [labels]="labels"
    [bars]="data"
    [showGrid]="true">
  </fin-bar-chart>
</div>
```

### Value Formatting

```typescript
// Currency formatting
<fin-bar-chart
  valuePrefix="$"
  valueSuffix=""
  [bars]="revenueData">
</fin-bar-chart>

// Percentage formatting
<fin-doughnut-chart
  valuePrefix=""
  valueSuffix="%"
  [values]="percentageData">
</fin-doughnut-chart>

// Custom units
<fin-area-chart
  valuePrefix=""
  valueSuffix=" units"
  [datasets]="productionData">
</fin-area-chart>
```

## Integration Examples

### Dashboard Widgets

```typescript
// Revenue dashboard with multiple chart types
<div class="dashboard-grid">
  <div class="revenue-chart">
    <h3>Quarterly Revenue</h3>
    <fin-bar-chart
      [labels]="quarters"
      [bars]="revenueData"
      valuePrefix="$"
      valueSuffix="M"
      [showGrid]="true">
    </fin-bar-chart>
  </div>

  <div class="traffic-chart">
    <h3>Traffic Sources</h3>
    <fin-doughnut-chart
      [labels]="trafficSources"
      [values]="trafficValues"
      [autoColors]="true"
      [size]="finSize.M"
      valueSuffix="%">
    </fin-doughnut-chart>
  </div>

  <div class="trend-chart">
    <h3>Growth Trend</h3>
    <fin-area-chart
      [labels]="months"
      [datasets]="growthData"
      [showGrid]="false">
    </fin-area-chart>
  </div>
</div>
```

### Dynamic Data Updates

```typescript
// Component with dynamic chart updates
export class DashboardComponent {
  chartData$ = this.dataService.getChartData();

  updateChartData() {
    this.dataService.refreshData();
  }
}

// Template with async data
<fin-bar-chart
  [labels]="(chartData$ | async)?.labels"
  [bars]="(chartData$ | async)?.datasets"
  [autoColors]="true">
</fin-bar-chart>
```

## Advanced Features

### Performance

- **Chart.js Integration**: Built on the powerful Chart.js library for optimal performance
- **Responsive Rendering**: Automatic chart resizing and redrawing
- **Memory Management**: Proper cleanup and chart destruction
- **Lazy Loading**: Charts are only rendered when visible

### Accessibility

- **ARIA Support**: Proper ARIA attributes for screen readers
- **Keyboard Navigation**: Accessible chart interaction
- **Color Accessibility**: High contrast color schemes available
- **Alternative Text**: Descriptive text for chart content

### Customization

- **Theme Integration**: Automatic integration with application themes
- **Custom Styling**: CSS customization support
- **Plugin Support**: Extensible with Chart.js plugins
- **Animation Control**: Configurable chart animations

### Data Handling

- **Real-time Updates**: Support for live data updates
- **Large Datasets**: Efficient handling of large data sets
- **Data Validation**: Built-in data validation and error handling
- **Format Flexibility**: Support for various data formats and structures
