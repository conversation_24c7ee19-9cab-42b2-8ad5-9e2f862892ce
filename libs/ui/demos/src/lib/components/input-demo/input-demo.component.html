<div class="fin-flex fin-justify-end">
  <fin-slide-toggle
    [formControl]="editModeControl"
    label="Edit mode"
  ></fin-slide-toggle>
</div>

<form [formGroup]="form" class="fin-w-[30rem]">
  <hr class="fin-my-6" finHorizontalSeparator />

  <fin-input
    label="Input"
    formControlName="input"
    [readonly]="!editModeControl.value"
  ></fin-input>

  <hr class="fin-my-6" finHorizontalSeparator />

  <fin-dropdown
    label="Autocomplete"
    formControlName="autocomplete"
    autocomplete
    [options]="options"
    [readonly]="!editModeControl.value"
  >
  </fin-dropdown>

  <hr class="fin-my-6" finHorizontalSeparator />

  <fin-dropdown
    label="Dropdown"
    formControlName="dropdown"
    [options]="options"
    [readonly]="!editModeControl.value"
  >
  </fin-dropdown>

  <hr class="fin-my-6" finHorizontalSeparator />

  <fin-dropdown
    label="Multiple"
    formControlName="multiple"
    [options]="options2"
    multiple
    [readonly]="!editModeControl.value"
  >
  </fin-dropdown>

  <hr class="fin-my-6" finHorizontalSeparator />

  <div class="fin-flex fin-justify-between">
    <fin-date-picker
      class="fin-block fin-w-[10rem]"
      formControlName="singleDate"
      label="Single date"
      showIcon
      [readonly]="!editModeControl.value"
    >
    </fin-date-picker>

    <fin-date-picker
      class="fin-block fin-w-[18rem]"
      formControlName="rangeDate"
      label="Range date"
      selectionMode="range"
      showIcon
      [readonly]="!editModeControl.value"
    >
    </fin-date-picker>
  </div>
</form>
