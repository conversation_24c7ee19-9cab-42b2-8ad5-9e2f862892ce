import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import {
  FormControl,
  NonNullableFormBuilder,
  ReactiveFormsModule,
} from '@angular/forms';
import { FinDatePickerModule } from '@fincloud/ui/date-picker';
import { FinDragDropModule } from '@fincloud/ui/drag-drop';
import { FIN_CUSTOM_MESSAGES, FinDropdownModule } from '@fincloud/ui/dropdown';
import { FIN_DATE_MASK, FIN_DEFAULT_REGION_ID, FIN_LOCALE_ID, FinInputModule, LocaleId } from '@fincloud/ui/input';
import { FinSeparatorsModule } from '@fincloud/ui/separators';
import { FinSlideToggleModule } from '@fincloud/ui/slide-toggle';

@Component({
  selector: 'fin-input-demo',
  standalone: true,
  imports: [
    CommonModule,
    FinInputModule,
    FinDropdownModule,
    FinDragDropModule,
    FinDatePickerModule,
    FinSlideToggleModule,
    FinSeparatorsModule,
    ReactiveFormsModule,
  ],
  templateUrl: './input-demo.component.html',
  styleUrl: './input-demo.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: FIN_CUSTOM_MESSAGES,
      useValue: {
        initialMessage: 'Enter text to search',
        noResultsMessage: 'No results found',
      },
    },
    {
      provide: FIN_DATE_MASK,
      useValue: {
        en: {
          dateFormat: 'dd/MM/yyyy',
        },
        de: {
          dateFormat: 'dd.MM.yyyy',
        },
      },
    },
    {
      provide: FIN_DEFAULT_REGION_ID,
      useValue: LocaleId.DE,
    },
    {
      provide: FIN_LOCALE_ID,
      useValue: LocaleId.DE,
    },
  ],
})
export class FinInputDemoComponent {
  editModeControl = new FormControl(true);
  form = this.fb.group({
    input: this.fb.control(
      'Lorem ipsum dolor sit amet consectetur, adipisicing elit. Blanditiis eos labore quod distinctio ducimus doloribus.',
    ),
    autocomplete: this.fb.control('value lorem'),
    dropdown: this.fb.control('value lorem'),
    multiple: this.fb.control(['value 1', 'value 2', 'value 3']),
    singleDate: this.fb.control('2025-07-01'),
    rangeDate: this.fb.control(['2025-07-01', '2025-07-10']),
  });
  options = [
    {
      label:
        'Lorem ipsum dolor sit amet consectetur, adipisicing elit. Blanditiis eos labore quod distinctio ducimus doloribus.',
      value: 'value lorem',
    },
    {
      label: 'Short label',
      value: 'value',
    },
  ];
  options2 = [
    {
      label: 'Option 1',
      value: 'value 1',
    },
    {
      label: 'Option 2',
      value: 'value 2',
    },
    {
      label: 'Option 3',
      value: 'value 3',
    },
    {
      label: 'Option 4',
      value: 'value 4',
    },
    {
      label: 'Option 5',
      value: 'value 5',
    },
  ];

  constructor(private fb: NonNullableFormBuilder) {}
}
