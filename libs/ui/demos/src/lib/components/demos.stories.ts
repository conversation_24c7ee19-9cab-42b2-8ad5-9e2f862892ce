import { CommonModule } from '@angular/common';
import {
  FIN_CURRENCY_MASK,
  FIN_DECIMAL_MASK,
  FIN_INTEGER_MASK,
  FIN_MONTHS_MASK,
  FIN_PERCENTAGE_MASK,
  FIN_REGION_LOCALE_ID,
  LocaleId,
} from '@fincloud/ui/input';
import {
  Meta,
  StoryObj,
  componentWrapperDecorator,
  moduleMetadata,
} from '@storybook/angular';
import { NgxCurrencyInputMode } from 'ngx-currency';
import { FinInputDemoComponent } from './input-demo/input-demo.component';

const MASK_CONFIG_BASE = {
  [LocaleId.DE]: {
    align: 'left',
    allowNegative: false,
    allowZero: true,
    decimal: ',',
    thousands: '.',
    nullable: true,
    min: null,
    max: null,
    inputMode: NgxCurrencyInputMode.Natural,
    precision: 0,
    prefix: '',
    suffix: '',
  },
  [LocaleId.EN]: {
    align: 'left',
    allowNegative: false,
    allowZero: true,
    decimal: '.',
    thousands: ',',
    nullable: true,
    min: null,
    max: null,
    inputMode: NgxCurrencyInputMode.Natural,
    precision: 0,
    prefix: '',
    suffix: '',
  },
};
const CURRENCY_MASK_CONFIG = {
  [LocaleId.DE]: {
    ...MASK_CONFIG_BASE[LocaleId.DE],
    precision: 2,
    suffix: ' €',
  },
  [LocaleId.EN]: {
    ...MASK_CONFIG_BASE[LocaleId.EN],
    precision: 2,
    prefix: '€',
  },
};
const PERCENTAGE_MASK_CONFIG = {
  [LocaleId.DE]: {
    ...MASK_CONFIG_BASE[LocaleId.DE],
    precision: 3,
    suffix: ' %',
  },
  [LocaleId.EN]: {
    ...MASK_CONFIG_BASE[LocaleId.EN],
    precision: 3,
    suffix: ' %',
  },
};
const DECIMAL_MASK_CONFIG = {
  [LocaleId.DE]: {
    ...MASK_CONFIG_BASE[LocaleId.DE],
    precision: 2,
  },
  [LocaleId.EN]: {
    ...MASK_CONFIG_BASE[LocaleId.EN],
    precision: 2,
  },
};

const meta: Meta = {
  title: 'Demos/Input',
  decorators: [
    moduleMetadata({
      imports: [CommonModule, FinInputDemoComponent],
      providers: [
        {
          provide: FIN_CURRENCY_MASK,
          useValue: CURRENCY_MASK_CONFIG,
        },
        {
          provide: FIN_PERCENTAGE_MASK,
          useValue: PERCENTAGE_MASK_CONFIG,
        },
        {
          provide: FIN_DECIMAL_MASK,
          useValue: DECIMAL_MASK_CONFIG,
        },
        {
          provide: FIN_MONTHS_MASK,
          useValue: MASK_CONFIG_BASE,
        },
        {
          provide: FIN_INTEGER_MASK,
          useValue: MASK_CONFIG_BASE,
        },
        {
          provide: FIN_REGION_LOCALE_ID,
          useValue: LocaleId.DE,
        },
      ],
    }),
    componentWrapperDecorator(
      (story) =>
        `<style>
          ::ng-deep .css-11xgcgt {
            display: none;
          }
        </style>

        ${story}`,
    ),
  ],
};

export default meta;
type Story = StoryObj;

export const Input: Story = {
  render: () => ({
    template: `
      <fin-input-demo></fin-input-demo>
    `,
  }),
};
