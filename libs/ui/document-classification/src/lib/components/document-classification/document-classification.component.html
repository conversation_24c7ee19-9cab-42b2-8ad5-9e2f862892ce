@if (state$ | async; as state) {
  <fin-accordion
    hideToggle
    [ngClass]="{
      'fin-document-classification-warning':
        (getMessage$ | async)?.type === 'warning',

      'fin-document-classification-success':
        (getMessage$ | async)?.type === 'success',
    }"
  >
    <fin-expansion-panel
      isSummaryAlwaysVisible
      [expanded]="state.expanded"
      [disabled]="state.expandDisabled || state.loading"
    >
      <ng-template #finTitle>
        @if (prefixTemplate) {
          <ng-container [ngTemplateOutlet]="prefixTemplate"></ng-container>
        }
      </ng-template>

      <ng-template #finDescription>
        @if (
          state.loading ||
          (state.rootFolderIdChanged && state.aiPrediction) ||
          state.readonly
        ) {
          <fin-document-classification-loading
            class="fin-block fin-w-full"
            [state]="state"
            [controlValue]="control.value"
            [maxWidth]="state.componentWidth"
            [btnCancelLabel]="btnCancelLabel"
            (canceledLoading)="onCancelLoading($event)"
            (updateState)="onUpdateState($event)"
          >
            @if (inputPrefixTemplate && !state.loading) {
              <ng-template #finInputPrefix>
                <ng-container
                  [ngTemplateOutlet]="inputPrefixTemplate"
                ></ng-container>
              </ng-template>
            }
          </fin-document-classification-loading>
        } @else {
          <fin-input
            class="fin-w-full fin-relative fin-z-[2]"
            [formControl]="control"
            [readonly]="state.readonly || state.loading"
            [placeholder]="state.placeholder || ''"
            (click)="onClick($event, state.readonly)"
            (blurred)="onBlur($event)"
            (keydown.space)="$event.stopPropagation()"
            (keydown.enter)="$event.stopPropagation()"
            [maxLength]="control.value ? state.maxLength : 0"
          >
            <ng-container finInputPrefix>
              @if (inputPrefixTemplate && !state.loading) {
                <ng-container
                  [ngTemplateOutlet]="inputPrefixTemplate"
                ></ng-container>
              }
            </ng-container>
            <ng-container finInputSuffix>
              @if (inputSuffixTemplate) {
                <ng-container
                  [ngTemplateOutlet]="inputSuffixTemplate"
                ></ng-container>
              }
            </ng-container>
          </fin-input>
        }
      </ng-template>

      <ng-template #finContent>
        @if (contentTemplate) {
          <div
            class="fin-text-color-text-tertiary fin-p-[0.8rem] fin-text-body-3-moderater"
          >
            <ng-container [ngTemplateOutlet]="contentTemplate"></ng-container>
          </div>
        }
      </ng-template>
    </fin-expansion-panel>
  </fin-accordion>
  <div class="fin-flex fin-justify-between fin-items-center">
    <div>
      @if ((getMessage$ | async)?.template && control.touched) {
        <mat-error>
          <div class="fin-inline-block fin-w-full">
            @if ((getMessage$ | async)?.template; as messageTemplate) {
              <ng-container *ngTemplateOutlet="messageTemplate"></ng-container>
            }
          </div>
        </mat-error>
      }
    </div>

    <div class="fin-flex fin-items-center">
      @if (footerSuffixTemplate) {
        <ng-container [ngTemplateOutlet]="footerSuffixTemplate"></ng-container>
      }
    </div>
  </div>
}
