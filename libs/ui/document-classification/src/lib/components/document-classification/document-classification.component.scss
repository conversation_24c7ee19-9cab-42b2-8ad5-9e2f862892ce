:host {
  display: block;

  ::ng-deep {
    .fin-accordion {
      > .fin-expansion-panel {
        border-radius: 0.8rem !important;
        > .mat-expansion-panel {
          border: 0.1rem solid theme('colors.color-border-default-primary');
          background-color: theme('colors.color-surface-hover');
          border-radius: 0.8rem !important;
          > .mat-expansion-panel-header {
            border-bottom: 0;
            min-height: 5.6rem;
            padding-right: 0;
            padding-left: 0.8rem !important;
            background-color: theme('colors.color-surface-primary');
            .mat-expansion-panel-header-title {
              flex-grow: 0;
              @apply fin-text-body-2-moderate;
            }
            .mat-expansion-panel-header-description {
              flex-grow: 1;
            }
          }
          &.mat-expanded {
            border: 0.1rem solid
              theme('colors.color-border-default-interactive');
            > .mat-expansion-panel-content {
              border-bottom: 0;
              .fin-panel-content {
                padding-left: 0 !important;
              }
            }
          }
        }
      }
      .mat-expansion-panel > .mat-expansion-panel-content .fin-panel-content {
        padding: 0;
      }
    }

    .fin-input {
      .fin-field-readonly,
      .fin-field-readonly.mat-focused {
        .mdc-text-field--outlined {
          .mdc-notched-outline__leading,
          .mdc-notched-outline__trailing,
          .mdc-notched-outline__notch {
            border: none;
          }
        }
      }
      .fin-field-readonly,
      .fin-field-readonly .mat-mdc-input-element {
        cursor: pointer;
      }
    }

    &.fin-document-classification {
      &-success {
        .mat-form-field-appearance-outline.ng-touched.ng-valid {
          --mdc-outlined-text-field-outline-color: theme(
            'colors.color-border-default-success'
          );
          --mdc-outlined-text-field-focus-outline-color: theme(
            'colors.color-border-default-success'
          );
          --mdc-outlined-text-field-hover-outline-color: theme(
            'colors.color-border-default-success'
          );
        }
      }
      &-warning {
        .mat-form-field-appearance-outline.ng-touched.ng-invalid {
          --mdc-outlined-text-field-error-outline-color: theme(
            'colors.color-border-default-warning'
          );
          --mdc-outlined-text-field-error-hover-outline-color: theme(
            'colors.color-border-default-warning'
          );
          --mdc-outlined-text-field-error-focus-outline-color: theme(
            'colors.color-border-default-warning'
          );
        }
      }
      &-error {
        .mat-form-field-appearance-outline.ng-touched.ng-invalid {
          --mdc-outlined-text-field-error-outline-color: theme(
            'colors.color-border-default-error'
          );
          --mdc-outlined-text-field-error-hover-outline-color: theme(
            'colors.color-border-default-error'
          );
          --mdc-outlined-text-field-error-focus-outline-color: theme(
            'colors.color-border-default-error'
          );
        }
      }
    }
  }

  &.fin-document-classification-loading ::ng-deep {
    .fin-accordion {
      > .fin-expansion-panel {
        > .mat-expansion-panel {
          > .mat-expansion-panel-header {
            &:hover {
              background-color: white !important;
              cursor: default;
            }
          }
        }
        &.mat-expanded {
          border: 0.1rem solid transparent !important;
        }
      }
    }
  }
}

@keyframes removeBlur {
  0% {
    filter: blur(8px);
    opacity: 0;
  }
  100% {
    filter: blur(0px);
    opacity: 1;
  }
}

@keyframes addBlur {
  0% {
    filter: blur(0px);
  }
  100% {
    filter: blur(8px);
  }
}
