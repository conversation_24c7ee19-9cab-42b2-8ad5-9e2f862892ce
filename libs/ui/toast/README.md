# @fincloud/ui/toast

A comprehensive Angular toast notification library providing non-intrusive feedback messages for user actions, system events, and status updates with customizable styling and behavior.

## Components & Services

### FinToastService

The main service for programmatically displaying toast notifications with different types and automatic dismissal.

**Key Features:**

- Programmatic toast creation and management
- Multiple toast types (success, error, info)
- Automatic dismissal with configurable duration
- Top positioning for optimal visibility
- Icon integration based on toast type
- Custom view container support
- Built on Angular Material Snackbar

**Basic Usage:**

```typescript
import { FinToastService, FinToast, FinToastType } from '@fincloud/ui/toast';

constructor(private toastService: FinToastService) {}

showSuccessToast() {
  const toast: FinToast = {
    message: 'Operation completed successfully!',
    type: FinToastType.SUCCESS
  };

  this.toastService.show(toast);
}

showErrorToast() {
  const toast: FinToast = {
    message: 'An error occurred. Please try again.',
    type: FinToastType.ERROR
  };

  this.toastService.show(toast);
}
```

### FinToastComponent (`fin-toast`)

The toast display component that renders the notification message with appropriate styling and icons.

**Key Features:**

- Automatic icon selection based on toast type
- Consistent styling and animations
- Message display with proper typography
- Responsive design for different screen sizes

## Models & Enums

### FinToast

The interface defining the structure of toast notifications.

```typescript
interface FinToast {
  message: string;
  type: FinToastType;
}
```

### FinToastType

Enumeration of available toast types with their semantic meanings.

```typescript
enum FinToastType {
  SUCCESS = 'success', // Positive feedback for successful operations
  ERROR = 'error', // Error messages and failure notifications
  INFO = 'info', // Informational messages and neutral updates
}
```

### FinToastIconType

Enumeration mapping toast types to their corresponding icons.

```typescript
enum FinToastIconType {
  SUCCESS = 'check', // Checkmark icon for success
  ERROR = 'exclamation', // Exclamation icon for errors
  INFO = 'info_i', // Info icon for informational messages
}
```

## Installation & Import

```typescript
import { FinToastModule, FinToastService } from '@fincloud/ui/toast';

// For creating toasts
import { FinToast, FinToastType } from '@fincloud/ui/toast';
```

## Toast Types & Usage

### Success Toasts

Use for positive feedback when operations complete successfully.

```typescript
const successToast: FinToast = {
  message: 'Data saved successfully!',
  type: FinToastType.SUCCESS,
};
this.toastService.show(successToast);
```

### Error Toasts

Use for error messages and failure notifications.

```typescript
const errorToast: FinToast = {
  message: 'Failed to save data. Please check your connection.',
  type: FinToastType.ERROR,
};
this.toastService.show(errorToast);
```

### Info Toasts

Use for informational messages and neutral updates.

```typescript
const infoToast: FinToast = {
  message: 'Your session will expire in 5 minutes.',
  type: FinToastType.INFO,
};
this.toastService.show(infoToast);
```

## Advanced Features

- **Automatic Dismissal**: Toasts automatically disappear after 3 seconds
- **Top Positioning**: Consistent positioning at the top of the viewport
- **Icon Integration**: Automatic icon selection based on toast type
- **Custom Styling**: Type-specific styling with consistent color schemes
- **View Container Support**: Optional custom view container for specific positioning
- **Accessibility**: Screen reader support and proper ARIA attributes
- **Animation**: Smooth slide-in and fade-out animations
- **Responsive Design**: Optimal display across different screen sizes and devices
