# @fincloud/ui/paginator

Angular pagination components for navigating through large datasets.

## Components

### FinPaginatorComponent (`fin-paginator`)

Pagination controls with page navigation and size selection.

**Basic Usage:**

```typescript
import { FinPaginatorModule } from '@fincloud/ui/paginator';

<fin-paginator
  [totalItems]="500"
  [pageSize]="20"
  [pageNumber]="1"
  [showPageSize]="true"
  (pageChange)="onPageChange($event)">
</fin-paginator>
```

### FinCompactPaginatorComponent (`fin-compact-paginator`)

Compact pagination with previous/next buttons only.

**Key Features:**

- Smart page number display
- Page size selection
- Go-to-page dropdown
- Compact variant available
