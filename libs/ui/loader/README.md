# @fincloud/ui/loader

A comprehensive Angular loader component library providing various loading indicators and progress animations for displaying loading states, progress feedback, and user wait experiences.

## Components

### FinLoaderComponent (`fin-loader`)

The main loader component that displays a spinning animation to indicate loading progress or waiting states.

**Key Features:**

- Spinning SVG animation with smooth rotation
- Conditional visibility with hide/show functionality
- Fixed size (2rem x 2rem) optimized for most use cases
- Lightweight and performant animation
- Accessibility support with proper alt text
- CSS-based animation for smooth performance

**Basic Usage:**

```typescript
import { FinLoaderModule } from '@fincloud/ui/loader';

// Basic loader
<fin-loader></fin-loader>

// Conditional loader
<fin-loader [hide]="!isLoading"></fin-loader>

// In loading states
<div class="loading-container">
  <fin-loader [hide]="!dataLoading"></fin-loader>
  <p *ngIf="dataLoading">Loading data...</p>
</div>
```

**Integration with Components:**

```typescript
// In buttons (uses internal dots loader)
<button fin-button [showLoader]="isSubmitting">
  Submit Form
</button>

// In overlays
<div class="overlay" *ngIf="isLoading">
  <fin-loader></fin-loader>
  <p>Please wait...</p>
</div>

// In data tables
<fin-table [rows]="data">
  <ng-template #finTableNoDataTemplate>
    <div class="text-center p-8">
      <fin-loader [hide]="!isLoadingData"></fin-loader>
      <p *ngIf="!isLoadingData && data.length === 0">No data available</p>
    </div>
  </ng-template>
</fin-table>
```

## Related Components

### FinDotsLoaderComponent (Internal)

A specialized dots loader used internally by button components for loading states.

**Key Features:**

- Three animated dots with staggered timing
- Multiple sizes (XS, S, M, L, XL)
- Blinking animation with opacity transitions
- Positioned for button integration
- Used automatically in `fin-button` with `showLoader`

### FinPercentageLoaderComponent (`fin-percentage-loader`)

A circular progress loader that displays completion percentage for determinate progress.

**Key Features:**

- Circular SVG progress indicator
- Percentage text display in center
- Configurable progress value (0-100%)
- Smooth progress transitions
- Fixed 64x64px size
- Stroke-based progress visualization

**Basic Usage:**

```typescript
import { FinPercentageLoaderModule } from '@fincloud/ui/percentage-loader';

<fin-percentage-loader [percentage]="uploadProgress"></fin-percentage-loader>

// With dynamic progress
<fin-percentage-loader [percentage]="completionPercentage"></fin-percentage-loader>
```

### FinProgressBarComponent (`fin-progress-bar`)

A horizontal progress bar component with support for multiple segments and tooltips.

**Key Features:**

- Multi-segment progress visualization
- Tooltip support for each segment
- Configurable colors and widths
- Min/max value display
- Interactive segment selection
- Customizable base colors

**Basic Usage:**

```typescript
import { FinProgressBarModule } from '@fincloud/ui/progress-bar';

<fin-progress-bar
  [segments]="progressSegments"
  [min]="0"
  [max]="100"
  amountSuffix="%"
  (selectedIndex)="onSegmentSelected($event)">
</fin-progress-bar>
```

## Installation & Import

```typescript
// Main loader
import { FinLoaderModule } from '@fincloud/ui/loader';

// Percentage loader
import { FinPercentageLoaderModule } from '@fincloud/ui/percentage-loader';

// Progress bar
import { FinProgressBarModule } from '@fincloud/ui/progress-bar';

// Or import individual components
import { FinLoaderComponent, FinPercentageLoaderComponent, FinProgressBarComponent } from '@fincloud/ui/loader';
```

## Usage Patterns

### Loading States

```typescript
// Component loading
<div *ngIf="isLoading; else content">
  <div class="flex items-center justify-center p-8">
    <fin-loader></fin-loader>
    <span class="ml-2">Loading...</span>
  </div>
</div>

<ng-template #content>
  <!-- Your content here -->
</ng-template>
```

### Form Submission

```typescript
// In forms
<form (ngSubmit)="onSubmit()">
  <!-- Form fields -->

  <button fin-button
    type="submit"
    [showLoader]="isSubmitting"
    [disabled]="isSubmitting">
    {{ isSubmitting ? 'Submitting...' : 'Submit' }}
  </button>
</form>
```

### File Upload Progress

```typescript
// File upload with percentage
<div class="upload-progress">
  <fin-percentage-loader [percentage]="uploadProgress"></fin-percentage-loader>
  <p>Uploading file... {{ uploadProgress }}%</p>
</div>
```

### Multi-step Progress

```typescript
// Multi-step process
<fin-progress-bar
  [segments]="[
    { width: 33, color: 'fin-bg-color-background-success-strong' },
    { width: 33, color: 'fin-bg-color-background-success-strong' },
    { width: 34, color: 'fin-bg-color-background-tertiary-minimal' }
  ]">
</fin-progress-bar>
```

## Advanced Features

### Performance

- **CSS Animations**: Hardware-accelerated CSS transforms for smooth animations
- **SVG Optimization**: Lightweight SVG assets for crisp rendering at any scale
- **Conditional Rendering**: Efficient show/hide logic to minimize DOM impact
- **Memory Management**: Proper cleanup and no memory leaks

### Accessibility

- **Screen Reader Support**: Proper alt text and ARIA attributes
- **Reduced Motion**: Respects user preferences for reduced motion
- **Focus Management**: Appropriate focus handling in loading states
- **Semantic HTML**: Proper semantic structure for assistive technologies

### Customization

- **CSS Variables**: Easy theming through CSS custom properties
- **Size Variants**: Multiple size options for different use cases
- **Color Schemes**: Support for light and dark themes
- **Animation Timing**: Configurable animation speeds and delays
