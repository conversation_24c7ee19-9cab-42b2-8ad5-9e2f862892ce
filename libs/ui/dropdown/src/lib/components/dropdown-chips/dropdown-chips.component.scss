::ng-deep .mat-mdc-chip-set.mdc-evolution-chip-set {
  .mdc-evolution-chip-set__chips {
    @apply fin-flex;
    @apply fin-flex-wrap;
    @apply fin-items-start;
    @apply fin-content-start;
    @apply fin-gap-x-size-spacing-16;
    @apply fin-gap-y-size-spacing-8;
    @apply fin-pt-[0.8rem] fin-pb-[0.4rem] fin-m-0;
    .mat-mdc-chip.mat-mdc-standard-chip {
      --mdc-chip-container-height: 40px;
      --mdc-chip-container-shape-radius: 9999px;
      @apply fin-px-size-spacing-16;
      @apply fin-m-0;
      @apply fin-bg-color-background-tertiary-minimal;
      @apply fin-border;
      @apply fin-border-color-border-default-primary;
      @apply hover:fin-bg-color-hover-tertiary;
      .mdc-evolution-chip__cell--primary {
        @apply fin-max-w-full;
        .mdc-evolution-chip__action--primary {
          @apply fin-p-0;
          @apply fin-max-w-full;
          .mdc-evolution-chip__text-label {
            @apply fin-flex;
            @apply fin-flex-row;
            @apply fin-gap-size-spacing-8;
            @apply fin-items-center;
            @apply fin-max-w-full;
            .text-label {
              @apply fin-text-text-body-2-size;
              @apply fin-text-color-text-primary;
              @apply fin-leading-text-body-2-line-height;
            }
          }
        }
      }
    }
  }
}
