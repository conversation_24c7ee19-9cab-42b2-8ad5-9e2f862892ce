# @fincloud/ui/dropdown

A comprehensive Angular dropdown component library providing select and autocomplete functionality with advanced features like multi-selection, search, custom templates, and chip display.

## Components

### FinDropdownComponent (`fin-dropdown`)

The main dropdown component that allows users to select one or multiple options from a list with extensive customization and search capabilities.

**Key Features:**

- Single and multi-selection modes
- Built-in search and filtering
- Custom option templates with prefix/suffix content
- Chip display for multi-selection
- Autocomplete functionality
- Custom message templates (no results, initial state)
- Reactive forms integration
- Keyboard navigation and accessibility
- Size variations (M, L)
- Avatar and icon support in options

**Basic Usage:**

```typescript
import { FinDropdownModule } from '@fincloud/ui/dropdown';

// In component
options = [
  { label: 'Option 1', value: 'opt1' },
  { label: 'Option 2', value: 'opt2' },
  { label: 'Option 3', value: 'opt3' }
];

// In template
<fin-dropdown
  label="Select an option"
  placeholder="Choose..."
  [options]="options"
  [formControl]="selectionControl">
</fin-dropdown>
```

**Multi-Selection Usage:**

```typescript
<fin-dropdown
  label="Select multiple options"
  [options]="options"
  [multiple]="true"
  [formControl]="multiSelectionControl">
</fin-dropdown>
```

## Directives

### Content Projection Directives

#### FinOptionLabelDirective (`finOptionLabel`)

Customizes the display of option labels with custom templates.

**Usage:**

```typescript
<fin-dropdown [options]="options">
  <ng-template finOptionLabel let-option>
    <strong>{{ option.label }}</strong>
    <small>{{ option.description }}</small>
  </ng-template>
</fin-dropdown>
```

#### FinOptionPrefixDirective (`finOptionPrefix`)

Adds prefix content (icons, avatars) to dropdown options.

**Usage:**

```typescript
<fin-dropdown [options]="options">
  <ng-template finOptionPrefix let-option>
    <fin-icon [name]="option.icon"></fin-icon>
  </ng-template>
</fin-dropdown>
```

#### FinOptionSuffixDirective (`finOptionSuffix`)

Adds suffix content to dropdown options.

**Usage:**

```typescript
<fin-dropdown [options]="options">
  <ng-template finOptionSuffix let-option>
    <span class="badge">{{ option.count }}</span>
  </ng-template>
</fin-dropdown>
```

#### FinChipPrefixDirective (`finChipPrefix`)

Customizes the prefix content of chips in multi-selection mode.

#### FinChipSuffixDirective (`finChipSuffix`)

Customizes the suffix content of chips in multi-selection mode.

### Message Directives

#### FinInitialMessageDirective (`finInitialMessage`)

Customizes the message shown when no search has been performed.

**Usage:**

```typescript
<fin-dropdown [options]="options">
  <ng-template finInitialMessage>
    <p>Start typing to search options...</p>
  </ng-template>
</fin-dropdown>
```

#### FinNoResultsMessageDirective (`finNoResultsMessage`)

Customizes the message shown when search returns no results.

**Usage:**

```typescript
<fin-dropdown [options]="options">
  <ng-template finNoResultsMessage>
    <p>No matching options found.</p>
  </ng-template>
</fin-dropdown>
```

## Models & Interfaces

### FinDropdownOption

The standard option interface for dropdown items.

```typescript
interface FinDropdownOption {
  label: string;
  value: any;
  disabled?: boolean;
  // Additional custom properties supported
}
```

## Installation & Import

```typescript
import { FinDropdownModule } from '@fincloud/ui/dropdown';

// Or import individual components and directives
import { FinDropdownComponent, FinOptionLabelDirective, FinOptionPrefixDirective, FinOptionSuffixDirective, FinChipPrefixDirective, FinChipSuffixDirective, FinInitialMessageDirective, FinNoResultsMessageDirective } from '@fincloud/ui/dropdown';
```

## Advanced Features

- **Custom Property Names**: Configure custom property names for label and value using `labelPropertyName` and `valuePropertyName`
- **Search Integration**: Built-in filtering with customizable search behavior
- **Chip Management**: Visual chip representation for multi-selection with remove functionality
- **Template Flexibility**: Extensive template customization for options, chips, and messages
- **Accessibility**: Full ARIA support, keyboard navigation, and screen reader compatibility
- **Performance**: Efficient rendering with virtual scrolling for large option lists
- **Reactive Forms**: Complete integration with Angular Reactive Forms and validation
- **Localization**: Support for custom messages and internationalization
