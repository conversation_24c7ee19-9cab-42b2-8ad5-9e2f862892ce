# @fincloud/ui/badges

A comprehensive Angular badges component library providing visual indicators for status, notifications, and categorization with support for icons, custom styling, and various badge types.

## Components

### FinBadgeStatusComponent (`fin-badge-status`)

A status badge component that displays status information with predefined styling for common status types.

**Key Features:**

- Predefined status types (Draft, Pending, Signed, Cancelled, In Progress)
- Multiple sizes (S, M)
- Icon integration with Material Icons or custom SVG
- Consistent status-based styling
- Text content support
- Responsive design

**Basic Usage:**

```typescript
import { FinBadgesModule } from '@fincloud/ui/badges';

// Basic status badge
<fin-badge-status
  type="Signed"
  text="Completed"
  size="m">
</fin-badge-status>

// Status badge with icon
<fin-badge-status
  type="Pending"
  text="In Review"
  iconName="schedule"
  size="s">
</fin-badge-status>

// Status badge with custom icon
<fin-badge-status
  type="Draft"
  text="Draft"
  iconSrc="assets/icons/draft.svg"
  size="m">
</fin-badge-status>
```

### FinBadgeIconComponent (`fin-badge-icon`)

An icon-focused badge component that provides visual indicators with icon support and various styling options.

**Key Features:**

- Multiple badge types (Default, Active, Attention, Inactive, Ellipse)
- Three size variants (XS, S, M)
- Material Icons and custom SVG support
- Type-based styling and colors
- Compact design for UI indicators

**Basic Usage:**

```typescript
// Icon badge with Material Icon
<fin-badge-icon
  type="Active"
  name="check"
  size="m">
</fin-badge-icon>

// Icon badge with custom SVG
<fin-badge-icon
  type="Attention"
  src="assets/icons/warning.svg"
  size="s">
</fin-badge-icon>

// Ellipse badge for notifications
<fin-badge-icon
  type="Ellipse"
  size="xs">
</fin-badge-icon>
```

### FinBadgeIndicatorComponent (`fin-badge-indicator`)

A notification badge component that displays counts and indicators, commonly used for showing unread messages or notifications.

**Key Features:**

- Numeric count display
- Multiple indicator types
- Automatic count formatting
- Overflow handling (99+)
- Compact design for overlays

**Basic Usage:**

```typescript
// Notification count badge
<fin-badge-indicator
  [count]="unreadMessages"
  type="Default">
</fin-badge-indicator>

// Active state indicator
<fin-badge-indicator
  [count]="activeItems"
  type="Active">
</fin-badge-indicator>

// Attention indicator
<fin-badge-indicator
  [count]="urgentItems"
  type="Attention">
</fin-badge-indicator>
```

## Models & Enums

### FinBadgeStatus

Enumeration of available status types for status badges.

```typescript
enum FinBadgeStatus {
  DRAFT = 'Draft', // Draft or preliminary status
  PENDING = 'Pending', // Awaiting action or approval
  SIGNED = 'Signed', // Completed or approved status
  CANCELLED = 'Cancelled', // Cancelled or rejected status
  IN_PROGRESS = 'InProgress', // Currently being processed
}
```

### FinBadgeType

Enumeration of available badge types for icon and indicator badges.

```typescript
enum FinBadgeType {
  ACTIVE = 'Active', // Active state (green)
  DEFAULT = 'Default', // Default state (neutral)
  ATTENTION = 'Attention', // Attention state (orange/red)
  INACTIVE = 'Inactive', // Inactive state (gray)
  ELLIPSE = 'Ellipse', // Simple ellipse indicator
  TRANSPARENT = 'Transparent', // Transparent background
}
```

## Configuration Options

### Badge Sizes

- `FinSize.XS` - Extra small (available for icon badges)
- `FinSize.S` - Small badges for compact layouts
- `FinSize.M` - Medium badges (default) for standard use

### Status Types

- `Draft` - Gray styling for draft or preliminary items
- `Pending` - Yellow/orange styling for items awaiting action
- `Signed` - Green styling for completed or approved items
- `Cancelled` - Red styling for cancelled or rejected items
- `InProgress` - Blue styling for items currently being processed

### Badge Types

- `Active` - Green styling for active states
- `Default` - Neutral styling for standard indicators
- `Attention` - Orange/red styling for important notifications
- `Inactive` - Gray styling for inactive states
- `Ellipse` - Simple circular indicator
- `Transparent` - Transparent background for subtle indicators

## Installation & Import

```typescript
import { FinBadgesModule } from '@fincloud/ui/badges';

// Or import individual components
import { FinBadgeStatusComponent, FinBadgeIconComponent, FinBadgeIndicatorComponent, FinBadgeStatus, FinBadgeType } from '@fincloud/ui/badges';
```

## Usage Examples

### Document Status Indicators

```typescript
// Document workflow status
<div class="document-list">
  <div class="document-item">
    <span>Contract.pdf</span>
    <fin-badge-status
      type="Signed"
      text="Signed"
      iconName="check_circle"
      size="s">
    </fin-badge-status>
  </div>

  <div class="document-item">
    <span>Proposal.pdf</span>
    <fin-badge-status
      type="Pending"
      text="Pending Review"
      iconName="schedule"
      size="s">
    </fin-badge-status>
  </div>

  <div class="document-item">
    <span>Draft.pdf</span>
    <fin-badge-status
      type="Draft"
      text="Draft"
      iconName="edit"
      size="s">
    </fin-badge-status>
  </div>
</div>
```

### Navigation with Notification Badges

```typescript
// Navigation menu with notification counts
<nav class="navigation">
  <a href="/messages" class="nav-item">
    <fin-icon name="mail"></fin-icon>
    Messages
    <fin-badge-indicator
      [count]="unreadMessages"
      type="Attention">
    </fin-badge-indicator>
  </a>

  <a href="/notifications" class="nav-item">
    <fin-icon name="notifications"></fin-icon>
    Notifications
    <fin-badge-indicator
      [count]="notifications"
      type="Default">
    </fin-badge-indicator>
  </a>
</nav>
```

### Table Row Status

```typescript
// Table with status badges
<fin-table [rows]="users" [columns]="columns">
  <ng-template name="statusTemplate" [finRowTemplate]="rows" let-user>
    <fin-badge-status
      [type]="user.status"
      [text]="user.statusText"
      size="s">
    </fin-badge-status>
  </ng-template>

  <ng-template name="actionsTemplate" [finRowTemplate]="rows" let-user>
    <fin-badge-icon
      [type]="user.isActive ? 'Active' : 'Inactive'"
      [name]="user.isActive ? 'check' : 'close'"
      size="s">
    </fin-badge-icon>
  </ng-template>
</fin-table>
```

### Tab Headers with Indicators

```typescript
// Tabs with notification indicators
<fin-tabs>
  <fin-tab>
    <ng-template finTabLabel>
      <div class="fin-flex fin-items-center fin-gap-2">
        <span>Inbox</span>
        <fin-badge-indicator
          [count]="inboxCount"
          type="Default">
        </fin-badge-indicator>
      </div>
    </ng-template>
    <ng-template finTabBody>
      <!-- Inbox content -->
    </ng-template>
  </fin-tab>

  <fin-tab>
    <ng-template finTabLabel>
      <div class="fin-flex fin-items-center fin-gap-2">
        <span>Urgent</span>
        <fin-badge-indicator
          [count]="urgentCount"
          type="Attention">
        </fin-badge-indicator>
      </div>
    </ng-template>
    <ng-template finTabBody>
      <!-- Urgent content -->
    </ng-template>
  </fin-tab>
</fin-tabs>
```

### Form Field Status

```typescript
// Form validation with status badges
<form [formGroup]="documentForm">
  <fin-input
    label="Document Title"
    formControlName="title">
  </fin-input>

  <div class="form-status">
    <fin-badge-status
      [type]="getDocumentStatus()"
      [text]="getStatusText()"
      size="s">
    </fin-badge-status>
  </div>
</form>
```

## Advanced Features

### Dynamic Badge Updates

```typescript
// Component with dynamic badge updates
export class NotificationComponent {
  unreadCount$ = this.notificationService.getUnreadCount();
  urgentCount$ = this.notificationService.getUrgentCount();

  getBadgeType(count: number): FinBadgeType {
    return count > 0 ? FinBadgeType.ATTENTION : FinBadgeType.DEFAULT;
  }
}

// Template with dynamic updates
<fin-badge-indicator
  [count]="unreadCount$ | async"
  [type]="getBadgeType(unreadCount$ | async)">
</fin-badge-indicator>
```

### Conditional Badge Display

```typescript
// Show badges only when needed
<div class="notification-icon">
  <fin-icon name="notifications"></fin-icon>
  <fin-badge-indicator
    *ngIf="notificationCount > 0"
    [count]="notificationCount"
    type="Attention">
  </fin-badge-indicator>
</div>
```

### Custom Badge Styling

```typescript
// Custom CSS classes for specific use cases
<fin-badge-status
  type="Signed"
  text="Verified"
  class="custom-verified-badge"
  size="m">
</fin-badge-status>
```

## Integration Examples

### With Dropdown Options

```typescript
<fin-dropdown [options]="statusOptions">
  <ng-template finOptionSuffix let-option>
    <fin-badge-status
      [type]="option.status"
      [text]="option.statusText"
      size="s">
    </fin-badge-status>
  </ng-template>
</fin-dropdown>
```

### With Button Actions

```typescript
<button fin-button appearance="secondary">
  <fin-icon name="inbox"></fin-icon>
  Inbox
  <fin-badge-indicator
    [count]="inboxCount"
    type="Default">
  </fin-badge-indicator>
</button>
```

### With Card Headers

```typescript
<div class="card-header">
  <h3>Document Status</h3>
  <fin-badge-status
    type="InProgress"
    text="Processing"
    iconName="sync"
    size="s">
  </fin-badge-status>
</div>
```

## Advanced Features

### Accessibility

- **ARIA Support**: Proper ARIA attributes for screen readers
- **Color Independence**: Status conveyed through text and icons, not just color
- **High Contrast**: Support for high contrast mode
- **Screen Reader**: Badge content is announced to assistive technologies

### Performance

- **Lightweight**: Minimal DOM impact with efficient rendering
- **Change Detection**: OnPush strategy for optimal performance
- **Memory Management**: Proper cleanup and no memory leaks
- **CSS Optimization**: Efficient styling with minimal CSS overhead

### Theming

- **Design System**: Consistent with application design system
- **Color Schemes**: Support for light and dark themes
- **Custom Colors**: Extensible color system for custom badge types
- **Responsive**: Consistent appearance across different screen sizes

### Best Practices

- **Semantic Usage**: Use appropriate badge types for their intended purpose
- **Consistent Sizing**: Maintain consistent badge sizes within the same context
- **Clear Messaging**: Use clear, concise text for status badges
- **Icon Selection**: Choose appropriate icons that reinforce the badge meaning
