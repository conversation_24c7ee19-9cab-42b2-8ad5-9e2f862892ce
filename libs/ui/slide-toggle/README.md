# @fincloud/ui/slide-toggle

Angular slide toggle component for boolean input controls.

## Components

### FinSlideToggleComponent (`fin-slide-toggle`)

Toggle switch component for on/off states.

**Basic Usage:**

```typescript
import { FinSlideToggleModule } from '@fincloud/ui/slide-toggle';

<fin-slide-toggle
  label="Enable notifications"
  [formControl]="toggleControl">
</fin-slide-toggle>
```

**Key Features:**

- Slide toggle animation
- Form integration
- Custom labels
- Disabled state support
