# @fincloud/ui/button

A comprehensive Angular button component library providing various button types and styles for user interactions, form submissions, and navigation actions.

## Components

### FinButtonComponent (`fin-button`)

The main button component that provides a clickable element for performing actions or submitting forms with extensive customization options.

**Key Features:**

- Multiple button appearances: primary, secondary, tertiary, danger, and more
- Various sizes: XS, S, M, L, XL
- Shape options: round and rectangle
- Loading state with built-in loader animation
- Attention highlighting for important actions
- Elevation effects
- Icon support with prefix/suffix positioning
- Active state management
- Full accessibility support

**Basic Usage:**

```typescript
import { FinButtonModule } from '@fincloud/ui/button';

// In template
<button fin-button
  appearance="primary"
  size="m"
  [showLoader]="isLoading">
  Save Changes
</button>
```

### FinButtonFabComponent (`fin-button-fab`)

A floating action button component for primary actions that need to be prominently displayed.

**Key Features:**

- Circular floating design
- Multiple sizes and appearances
- Icon-focused design
- Elevation effects
- Positioning flexibility

**Basic Usage:**

```typescript
<button fin-button-fab size="l">
  <fin-icon name="add"></fin-icon>
</button>
```

### FinButtonLinkComponent (`fin-button-link`)

A button component styled as a link for navigation and secondary actions.

**Key Features:**

- Link-like appearance with button functionality
- Hover and focus states
- Size variations
- Icon support

**Basic Usage:**

```typescript
<button fin-button-link>
  <fin-icon name="external-link"></fin-icon>
  Learn More
</button>
```

### FinButtonActionComponent (`fin-button-action`)

A specialized button component for action-specific use cases with predefined styling.

**Key Features:**

- Action-oriented design
- Consistent styling for common actions
- Size and state management
- Icon integration

**Basic Usage:**

```typescript
<button fin-button-action size="m">
  <fin-icon name="settings"></fin-icon>
</button>
```

## Installation & Import

```typescript
import { FinButtonModule } from '@fincloud/ui/button';

// Or import individual components
import { FinButtonComponent, FinButtonFabComponent, FinButtonLinkComponent, FinButtonActionComponent } from '@fincloud/ui/button';
```

## Button Appearances

- `primary` - Main call-to-action buttons
- `secondary` - Secondary actions
- `tertiary` - Subtle actions
- `danger` - Destructive actions
- `success` - Positive confirmations
- `warning` - Cautionary actions

## Button Shapes

- `round` - Rounded corners (default)
- `rectangle` - Sharp corners

## Advanced Features

- **Loading States**: Built-in loader animation with `showLoader` property
- **Attention Mode**: Highlight important buttons with `attention` property
- **Active States**: Maintain hover appearance with `isActive` property
- **Elevation**: Add depth with `elevation` property
- **Icon Integration**: Support for prefix and suffix icons
- **Accessibility**: Full ARIA support and keyboard navigation
- **Responsive Design**: Consistent appearance across different screen sizes
