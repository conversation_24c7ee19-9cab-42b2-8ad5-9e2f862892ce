# @fincloud/ui/progress-bar

Angular progress bar component for displaying completion progress.

## Components

### FinProgressBarComponent (`fin-progress-bar`)

Horizontal progress bar with multi-segment support.

**Basic Usage:**

```typescript
import { FinProgressBarModule } from '@fincloud/ui/progress-bar';

<fin-progress-bar
  [segments]="progressSegments"
  [min]="0"
  [max]="100">
</fin-progress-bar>
```

**Key Features:**

- Multi-segment progress visualization
- Tooltip support
- Configurable colors
- Min/max value display
