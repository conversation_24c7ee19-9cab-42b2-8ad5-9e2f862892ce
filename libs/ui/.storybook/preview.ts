import { registerLocaleData } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import localeDe from '@angular/common/locales/de';
import localeDeExtra from '@angular/common/locales/extra/de';
import { importProvidersFrom } from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { FIN_LOCALE_ID, FIN_REGION_LOCALE_ID } from '@fincloud/ui/input';
import { FinAngularMaterialModule } from '@fincloud/utils/angular-material';
import { setCompodocJson } from '@storybook/addon-docs/angular';
import {
  Preview,
  applicationConfig,
  componentWrapperDecorator,
} from '@storybook/angular';
import * as docJson from '../documentation.json'; // The path to your generated json file from Compodoc contains all your documentation information.
import { version } from '../package.json';

setCompodocJson(docJson);
registerLocaleData(localeDe, 'de', localeDeExtra);

const [major, minor, patch] = version.split('.');
const preview: Preview = {
  globalTypes: {
    region: {
      name: 'region',
      description: 'Applies regional settings',
      defaultValue: 'de',
      toolbar: {
        icon: 'globe',
        items: [
          { value: 'en', title: 'United Kingdom' },
          { value: 'de', title: 'Germany' },
        ],
      },
    },
    theme: {
      description: 'Global theme for components',
      toolbar: {
        // The label to show for this toolbar item
        title: 'Theme',
        icon: 'wand',
        // Array of plain string values or MenuItem shape (see below)
        items: ['theme-neoshare', 'theme-volksbank'],
        // Change title based on selected value
        dynamicTitle: true,
      },
    },
  },
  decorators: [
    (storyFnc, context) => {
      const locale = context.globals['region'];
      // Your theme provider and other context providers goes in the return statement
      const story = storyFnc(context);

      story.moduleMetadata?.providers?.push({
        provide: FIN_REGION_LOCALE_ID,
        useValue: locale,
      });
      story.moduleMetadata?.providers?.push({
        provide: FIN_LOCALE_ID,
        useValue: locale,
      });
      return story;
    },
    // Apply application config to all stories
    applicationConfig({
      // List of providers and environment providers that should be available to the root component and all its children.
      providers: [
        importProvidersFrom(HttpClientModule),
        importProvidersFrom(BrowserAnimationsModule),
        importProvidersFrom(FinAngularMaterialModule),
      ],
    }),
    componentWrapperDecorator(
      (story) => `<div [attr.data-theme]="myTheme">${story} </div>`,
      ({ globals }) => {
        return { myTheme: globals['theme'] };
      },
    ),
  ],

  parameters: {
    version: {
      major,
      minor,
      patch,
    },
    layout: 'centered',
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
    docs: {
      toc: true,
      title: 'On this page',
    },
  },

  tags: ['autodocs'],
};

export default preview;
