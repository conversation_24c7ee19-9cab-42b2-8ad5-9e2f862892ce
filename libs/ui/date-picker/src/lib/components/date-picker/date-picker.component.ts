import { ConnectedPosition, OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ContentChild,
  DestroyRef,
  EventEmitter,
  Inject,
  Injector,
  Input,
  OnChanges,
  OnInit,
  Optional,
  Output,
  SimpleChanges,
  TemplateRef,
  booleanAttribute,
  forwardRef,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  FormControl,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatError, MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FinAiSuggestionComponent } from '@fincloud/ui/ai-suggestion';
import { FinButtonModule } from '@fincloud/ui/button';
import {
  FinFieldMessageBodyDirective,
  FinFieldMessageService,
} from '@fincloud/ui/field-message';
import { FinIconModule } from '@fincloud/ui/icon';
import {
  FIN_DATE_MASK,
  FIN_REGION_LOCALE_ID,
  FinDateMaskConfig,
  FinInputModule,
  LocaleId,
} from '@fincloud/ui/input';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinErrorSpace, FinSize } from '@fincloud/ui/types';
import {
  FIN_MAT_FORM_FIELD_DEFAULT_OPTIONS,
  FinAngularMaterialModule,
} from '@fincloud/utils/angular-material';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import { FinFieldService } from '@fincloud/utils/services';
import { format, isValid, parse } from 'date-fns';
import { compact, isArray } from 'lodash-es';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { PrimeNGConfig } from 'primeng/api';
import { CalendarModule } from 'primeng/calendar';
import { pairwise, shareReplay, startWith } from 'rxjs';
import { FinDatePickerIntl } from '../../models/fin-date-picker-intl';
import { FinDatePickerSelectionMode } from '../../models/fin-date-picker-selection-mode';
import { FinDatePickerView } from '../../models/fin-date-picker-view';
import { DateRangePipe } from '../../pipes/date-range.pipe';
import { FinDateAdapterService } from '../../services/date-adapter.service';
import { DATE_FORMAT } from '../../utils/date-format';
import { FIN_DATE_PICKER_INTL } from '../../utils/date-picker-intl';
import { FIN_DATE_PICKER_LOCALE } from '../../utils/date-picker-locale-token';
import { DEFAULT_POSITION_LIST } from '../../utils/default-position-list';
import { stringToDate } from '../../utils/string-to-date';

/**
 * A component that allows users to select a date from a calendar popup.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-date-picker--docs Storybook Reference}
 */

@Component({
  selector: 'fin-date-picker',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatError,
    FinIconModule,
    CalendarModule,
    OverlayModule,
    MatFormFieldModule,
    MatInputModule,
    FinInputModule,
    FinAngularMaterialModule,
    FinTruncateTextModule,
    FinButtonModule,
    NgxMaskDirective,
    FinAiSuggestionComponent,
  ],
  templateUrl: './date-picker.component.html',
  styleUrl: './date-picker.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    '[class]': 'datePickerCssClasses',
  },
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinDatePickerComponent),
      multi: true,
    },
    FIN_MAT_FORM_FIELD_DEFAULT_OPTIONS,
    FinFieldService,
    FinFieldMessageService,
    FinDateAdapterService,
    DateRangePipe,
    provideNgxMask(),
  ],
})
export class FinDatePickerComponent
  extends FinControlValueAccessor
  implements OnInit, AfterViewInit, OnChanges
{
  /** Label for the field */
  @Input() label = '';

  /** The placeholder for this control. */
  @Input() placeholder = '';

  /** Size of the input. */
  @Input() size: FinSize.M | FinSize.L = FinSize.M;

  /** Size of the input. */
  @Input() selectionMode: FinDatePickerSelectionMode = 'single';

  /** Specifies the calendar selection mode.*/
  @Input() view: FinDatePickerView = 'date';

  /** Specifies the min date in format 'yyyy-MM-dd' or date object.*/
  @Input()
  set minDate(date: Date | string) {
    this.minDateParsed = stringToDate(date);
  }

  /** Specifies the max date in format 'yyyy-MM-dd' or date object.*/
  @Input()
  set maxDate(date: Date | string) {
    this.maxDateParsed = stringToDate(date);
  }

  /** Array with dates that should be disabled.*/
  @Input() disabledDates: Date[] = [];

  /** Specifies when the calendar icon is visible.*/
  @Input({ transform: booleanAttribute }) showIcon = false;

  /** Specifies when the button bar is shown. */
  @Input({ transform: booleanAttribute }) showButtonBar = false;

  /** Whether the form field should reserve space for error. */
  @Input() dynamicErrorSpace!: FinErrorSpace;

  /** Switch readonly mode. */
  @Input({ transform: booleanAttribute }) readonly = false;

  /** To be used only with external messages */
  @Input() externalFieldMessages: FinFieldMessageBodyDirective | null = null;

  /**
   * Enables AI suggestion animations. When `true`, suggestions appear on input changes and auto-hide after certain time.
   * Animation only triggers when BOTH `aiEnabled` is `true` AND input value changes.
   */
  @Input({ transform: booleanAttribute }) aiEnabled = false;

  /** Event emitted when a date is selected. */
  @Output() selectDate = new EventEmitter<string | string[]>();

  /** Event emitted when the calendar is closed. */
  @Output() closeCalendar = new EventEmitter<void>();

  /** Event emitted when the clear button is clicked. */
  @Output() clearDate = new EventEmitter<void>();

  /** Event emitted when the input is changed. */
  @Output() inputChange = new EventEmitter<void>();

  /**
   * Emitted when the AI suggestion animation completes.
   * Only emits when the animation completes naturally, not when `aiEnabled` is disabled.
   */
  @Output() aiSuggestionReady = new EventEmitter<void>();

  @ContentChild('finFooter')
  protected footerTemplate!: TemplateRef<unknown>;

  protected get datePickerCssClasses(): string {
    return `fin-date-picker`;
  }

  /** Indicates whether the calendar popup is currently open. */
  protected isOpen = false;

  /** List of preferred positions for the CDK overlay. */
  protected defaultPositionList = DEFAULT_POSITION_LIST;

  /** Internal form control bound to the masked input field. */
  protected internalDateField = new FormControl();

  /** Internal form control bound to the PrimeNG calendar component. */
  protected internalDatepicker = new FormControl();

  /** Returns the active validation message. */
  protected getMessage$ = this.finFieldMessageService?.getMessage$.pipe(
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  /** Default date format used by PrimeNG when no other is provided. */
  protected defaultValueFormat = 'yy-mm-dd';

  protected sizes = FinSize;
  protected mask = '';
  protected minDateParsed: Date | null = null;
  protected maxDateParsed: Date | null = null;

  constructor(
    injector: Injector,
    private destroyRef: DestroyRef,
    private finFieldService: FinFieldService,
    private dateRangePipe: DateRangePipe,
    private primeNgConfig: PrimeNGConfig,
    private finDateAdapterService: FinDateAdapterService,

    @Optional() private finFieldMessageService: FinFieldMessageService,

    @Optional()
    @Inject(FIN_DATE_PICKER_LOCALE)
    private locale: FinDatePickerIntl,

    @Optional()
    @Inject(FIN_REGION_LOCALE_ID)
    private regionalLocaleId: LocaleId,

    @Optional()
    @Inject(FIN_DATE_MASK)
    private finDateMask: Record<LocaleId, FinDateMaskConfig>,
  ) {
    super(injector);
    this.primeNgConfig.setTranslation(this.locale || FIN_DATE_PICKER_INTL);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['readonly']) {
      const { currentValue, previousValue } = changes['readonly'];

      if (currentValue) {
        this.control.disable();
      }

      if (!currentValue && previousValue) {
        this.control.enable();
      }
    }
  }

  ngOnInit(): void {
    this.defaultPositionList = this.updateOverlayPosition(
      this.dynamicErrorSpace,
    );
  }

  ngAfterViewInit() {
    this.control.valueChanges
      .pipe(startWith(this.control.value), takeUntilDestroyed(this.destroyRef))
      .subscribe((selectedDate: string | string[]) => {
        this.updateInternalControls(selectedDate);
      });

    this.internalDateField.valueChanges
      .pipe(
        startWith(this.internalDateField.value),
        pairwise(),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(([previous, current]) => {
        this.updateControlsOnManualInput(previous, current);
      });

    // TODO: Check why (onClear)="onClearDate()" is not emitting
    this.internalDatepicker.valueChanges
      .pipe(
        startWith(this.internalDatepicker.value),
        pairwise(),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(([previous, current]) => {
        if (previous && !current) {
          this.onClearDate();
        }
      });

    this.mask = this.getMask(
      this.finDateMask[this.regionalLocaleId].dateFormat,
    );
  }

  /**
   * Synchronises the internal form controls (text field & calendar picker)
   * with the external form control value.
   *
   * @param selectedDate - The date value(s) coming from the form control.
   */
  private updateInternalControls(selectedDate: string | string[]) {
    const { errors, status, touched, dirty } = this.control;
    const dateFieldViewValue = this.dateRangePipe.transform(selectedDate);
    this.internalDateField.setValue(dateFieldViewValue);

    if (
      this.selectionMode === 'range' &&
      isArray(selectedDate) &&
      compact(selectedDate).length === 2
    ) {
      const parsedDate = selectedDate.map((date) =>
        parse(date, DATE_FORMAT, new Date()),
      );
      this.internalDatepicker.setValue(parsedDate);
    }

    if (this.selectionMode === 'single') {
      this.internalDatepicker.setValue(selectedDate);
    }

    if (status === 'DISABLED') {
      this.internalDateField.disable();
    } else {
      this.internalDateField.enable();
    }

    if (touched) {
      this.internalDateField.markAsTouched();
    } else {
      this.internalDateField.markAsUntouched();
    }

    if (dirty) {
      this.internalDateField.markAsDirty();
    } else {
      this.internalDateField.markAsPristine();
    }

    this.finFieldService.controlErrors$.next(errors);
    this.internalDateField.setErrors(errors);
  }

  /**
   * Applies the date(s) chosen from the datepicker popup
   * to the external form control and internal date field control
   */
  protected onSelectDate() {
    const selectedDate: Date | Date[] = this.internalDatepicker.value;
    let modelValue: string | string[] = '';

    if (
      this.selectionMode === 'range' &&
      isArray(selectedDate) &&
      compact(selectedDate).length === 2
    ) {
      modelValue = selectedDate.map((date) =>
        this.finDateAdapterService.fromModel(date),
      );
    }

    if (this.selectionMode === 'single' && !isArray(selectedDate)) {
      modelValue = this.finDateAdapterService.fromModel(selectedDate);
    }

    if (modelValue) {
      this.control.setValue(modelValue, { emitEvent: false });
      this.selectDate.emit(modelValue);
    }

    const internalDateFieldValue = this.dateRangePipe.transform(
      this.internalDatepicker.value,
    );
    this.internalDateField.setValue(internalDateFieldValue);

    this.closeDatepicker(selectedDate);
  }

  /**
   * Closes the datepicker if the required number of dates has been
   * selected for the current `selectionMode`.
   *
   * @param selectedDate - The date value(s) that were just selected.
   */
  private closeDatepicker(selectedDate: Date | Date[]) {
    if (
      this.selectionMode === 'single' ||
      (this.selectionMode === 'range' &&
        isArray(selectedDate) &&
        compact(selectedDate).length === 2)
    ) {
      this.isOpen = false;
    }
  }

  protected onCloseCalendar() {
    this.closeCalendar.emit();
  }

  private onClearDate() {
    this.clearDate.emit();
    this.control.reset();
    this.isOpen = false;
  }

  /**
   * Calculates a position strategy for the overlay that accounts for the
   * selected error‑space handling mode.
   *
   * @param dynamicErrorSpace - How the form‑field reserves space for error messages.
   * @returns The list of preferred positions for the CDK overlay.
   */
  private updateOverlayPosition(
    dynamicErrorSpace: FinErrorSpace,
  ): ConnectedPosition[] {
    if (dynamicErrorSpace === 'fixed') {
      const overlayPositionsOffset = DEFAULT_POSITION_LIST;
      overlayPositionsOffset[0].offsetY = -21;
      overlayPositionsOffset[3].offsetY = -21;

      if (this.label.length) {
        overlayPositionsOffset[1].offsetY = 21;
        overlayPositionsOffset[2].offsetY = 21;
      }

      return overlayPositionsOffset;
    }

    return DEFAULT_POSITION_LIST;
  }

  protected openDatePicker(event: Event): void {
    this.isOpen = true;
    event.stopPropagation();
  }

  protected closeDatePicker(): void {
    this.isOpen = false;
  }

  private updateControlsOnManualInput(previous: string, current: string) {
    const { dirty } = this.internalDateField;

    if (this.selectionMode === 'single') {
      if (current?.length === 8) {
        const modelValueParsed = parse(current, 'ddMMyyyy', new Date());
        if (isValid(modelValueParsed)) {
          const modelValue = format(modelValueParsed, DATE_FORMAT);

          this.control.setValue(modelValue, { emitEvent: false });
          this.updateInternalControls(modelValue);
          this.isOpen = false;
        } else {
          // Invalid date, will trigger validations
          this.control.setValue(current, {
            emitEvent: false,
          });
        }
      }
    }

    if (this.selectionMode === 'range') {
      if (current?.length === 16) {
        const firstDate = current.substring(0, 8);
        const secondDate = current.substring(8, 16);
        const firstDateParsed = parse(firstDate, 'ddMMyyyy', new Date());
        const secondDateParsed = parse(secondDate, 'ddMMyyyy', new Date());

        if (isValid(firstDateParsed) && isValid(secondDateParsed)) {
          const firstDateModelValue = format(firstDateParsed, DATE_FORMAT);
          const secondDateModelValue = format(secondDateParsed, DATE_FORMAT);
          const datesArray = [firstDateModelValue, secondDateModelValue];

          this.control.setValue(datesArray, {
            emitEvent: false,
          });
          this.updateInternalControls(datesArray);
          this.isOpen = false;
        } else {
          // Invalid date, will trigger validations
          this.control.setValue([firstDate, secondDate], {
            emitEvent: false,
          });
        }
      }

      // Once a range is picked and after that only the start date is selected, reset the control because
      // if an end date is not selected the control remains populated with the previous range
      // but no actual range is selected thru the calendar.
      if (previous && current === null) {
        this.control.reset();
      }
    }

    // On manual delete
    if (previous && current === '') {
      this.internalDatepicker.reset();
      this.control.reset();
    }

    this.internalDateField.updateValueAndValidity({ emitEvent: false });
    if (this.internalDateField.invalid) {
      this.control.setValue(this.internalDateField.value, {
        emitEvent: false,
      });
    }

    if (dirty) {
      this.control.markAsDirty();
    } else {
      this.control.markAsPristine();
    }

    this.internalDateField.setErrors(this.control.errors);
    this.finFieldService.controlErrors$.next(this.control.errors);

    this.inputChange.emit();
  }

  private getMask(dateFormat: string) {
    let separator = '';
    let mask = '';

    for (const char of dateFormat) {
      if (!/[a-zA-Z0-9]/.test(char)) {
        separator = char;
        break;
      }
    }

    if (this.selectionMode === 'single') {
      mask = `00${separator}00${separator}0000`;
    } else {
      mask = `00${separator}00${separator}0000 - 00${separator}00${separator}0000`;
    }

    return mask;
  }

  protected onBlur(event: Event) {
    const { touched } = this.internalDateField;

    if (touched) {
      this.control.markAsTouched();
    } else {
      this.control.markAsUntouched();
    }

    this.blur(event);
  }
  protected onAiSuggestionReady() {
    this.aiSuggestionReady.emit();
  }

  protected lockReadonlyScroll(input: HTMLInputElement) {
    if (this.readonly) {
      input.scrollLeft = 0;
    }
  }
}
