# @fincloud/ui/date-picker

A comprehensive Angular date picker component library providing calendar-based date selection with support for single dates, date ranges, different views, and extensive customization options.

## Components

### FinDatePickerComponent (`fin-date-picker`)

The main date picker component that provides an input field with calendar overlay for date selection with extensive configuration options.

**Key Features:**

- Single date and date range selection modes
- Multiple calendar views (date, month, year)
- Min/max date constraints
- Disabled dates configuration
- Calendar icon toggle
- Button bar for quick actions
- Internationalization support
- Reactive forms integration with ControlValueAccessor
- Built-in validation state display
- Multiple sizes (M, L)
- Readonly mode support
- Built on PrimeNG Calendar with custom styling

**Basic Usage:**

```typescript
import { FinDatePickerModule } from '@fincloud/ui/date-picker';

// In component
dateControl = new FormControl(new Date());

// In template
<fin-date-picker
  label="Select Date"
  placeholder="dd/mm/yyyy"
  [formControl]="dateControl"
  [showIcon]="true"
  size="m">
</fin-date-picker>
```

**Date Range Selection:**

```typescript
// In component
dateRangeControl = new FormControl([]);

// In template
<fin-date-picker
  label="Select Date Range"
  placeholder="Select start and end dates"
  [formControl]="dateRangeControl"
  selectionMode="range"
  [showIcon]="true"
  [showButtonBar]="true">
</fin-date-picker>
```

**Month/Year Selection:**

```typescript
<fin-date-picker
  label="Select Month"
  [formControl]="monthControl"
  view="month"
  placeholder="mm/yyyy"
  [showIcon]="true">
</fin-date-picker>

<fin-date-picker
  label="Select Year"
  [formControl]="yearControl"
  view="year"
  placeholder="yyyy"
  [showIcon]="true">
</fin-date-picker>
```

**Date Constraints:**

```typescript
// In component
constrainedDateControl = new FormControl();
minDate = new Date('2024-01-01');
maxDate = new Date('2024-12-31');
disabledDates = [
  new Date('2024-12-25'), // Christmas
  new Date('2024-01-01')  // New Year
];

// In template
<fin-date-picker
  label="Select Business Date"
  [formControl]="constrainedDateControl"
  [minDate]="minDate"
  [maxDate]="maxDate"
  [disabledDates]="disabledDates"
  [showIcon]="true">
</fin-date-picker>
```

## Configuration Options

### Selection Modes

- `single` - Single date selection (default)
- `range` - Date range selection (start and end dates)

### Calendar Views

- `date` - Day-level date selection (default)
- `month` - Month-level selection
- `year` - Year-level selection

### Size Options

- `FinSize.M` - Medium date picker (default)
- `FinSize.L` - Large date picker

### Date Picker Properties

- `label` - Label text displayed above the input
- `placeholder` - Placeholder text shown in the input field
- `minDate` - Minimum selectable date (Date object or 'yyyy-MM-dd' string)
- `maxDate` - Maximum selectable date (Date object or 'yyyy-MM-dd' string)
- `disabledDates` - Array of dates that should be disabled
- `showIcon` - Whether to display the calendar icon
- `showButtonBar` - Whether to show the button bar with Today/Clear buttons
- `readonly` - Make the date picker read-only

## Installation & Import

```typescript
import { FinDatePickerModule } from '@fincloud/ui/date-picker';

// Or import individual components
import { FinDatePickerComponent } from '@fincloud/ui/date-picker';
```

## Models & Types

### FinDatePickerSelectionMode

```typescript
type FinDatePickerSelectionMode = 'single' | 'range';
```

### FinDatePickerView

```typescript
type FinDatePickerView = 'date' | 'month' | 'year';
```

### FinDatePickerIntl

Interface for internationalization configuration:

```typescript
interface FinDatePickerIntl {
  firstDayOfWeek: number; // 0 = Sunday, 1 = Monday, etc.
  dayNames: string[]; // Full day names
  dayNamesShort: string[]; // Short day names
  dayNamesMin: string[]; // Minimal day names
  monthNames: string[]; // Full month names
  monthNamesShort: string[]; // Short month names
  today: string; // "Today" button text
  clear: string; // "Clear" button text
}
```

## Event Handling

### Available Events

- `selectDate` - Emitted when a date is selected (returns string or string[] for ranges)

### Event Usage

```typescript
<fin-date-picker
  (selectDate)="onDateSelected($event)"
  [formControl]="dateControl">
</fin-date-picker>

// In component
onDateSelected(selectedDate: string | string[]) {
  if (Array.isArray(selectedDate)) {
    console.log('Date range selected:', selectedDate);
  } else {
    console.log('Single date selected:', selectedDate);
  }
}
```

## Advanced Usage Examples

### Form Integration

```typescript
// In component
bookingForm = this.fb.group({
  checkIn: ['', Validators.required],
  checkOut: ['', Validators.required],
  guests: [1, [Validators.required, Validators.min(1)]]
});

// In template
<form [formGroup]="bookingForm">
  <fin-date-picker
    label="Check-in Date"
    formControlName="checkIn"
    [minDate]="today"
    [showIcon]="true">
  </fin-date-picker>

  <fin-date-picker
    label="Check-out Date"
    formControlName="checkOut"
    [minDate]="checkInDate"
    [showIcon]="true">
  </fin-date-picker>
</form>
```

### Validation Integration

```typescript
<fin-date-picker
  label="Birth Date"
  [formControl]="birthDateControl"
  [maxDate]="today"
  [showIcon]="true">

  <fin-field-messages>
    <ng-template finFieldMessage type="error" errorKey="required">
      Birth date is required
    </ng-template>
    <ng-template finFieldMessage type="warning" errorKey="futureDate">
      Birth date cannot be in the future
    </ng-template>
  </fin-field-messages>
</fin-date-picker>
```

### Business Date Selection

```typescript
// Exclude weekends and holidays
getBusinessDays(): Date[] {
  const holidays = [
    new Date('2024-12-25'),
    new Date('2024-01-01'),
    new Date('2024-07-04')
  ];

  const weekends = this.getWeekendDates();
  return [...holidays, ...weekends];
}

<fin-date-picker
  label="Business Date"
  [formControl]="businessDateControl"
  [disabledDates]="getBusinessDays()"
  [showIcon]="true"
  [showButtonBar]="true">
</fin-date-picker>
```

## Advanced Features

### Internationalization

The date picker supports full internationalization with customizable:

- Day and month names
- First day of the week
- Date formats
- Button labels

### Accessibility

- **ARIA Support**: Full ARIA attributes for screen readers
- **Keyboard Navigation**: Complete keyboard navigation within the calendar
- **Focus Management**: Proper focus indicators and management
- **Label Association**: Proper label-input association for assistive technologies

### Performance

- **Overlay Management**: Efficient overlay positioning and management
- **Change Detection**: OnPush strategy for optimal performance
- **Memory Management**: Proper cleanup and no memory leaks
- **Lazy Loading**: Calendar is only rendered when opened

### Responsive Design

- **Mobile Friendly**: Touch-friendly calendar interaction
- **Flexible Sizing**: Adapts to container width
- **Overlay Positioning**: Smart positioning to stay within viewport
- **Consistent Styling**: Maintains appearance across different screen sizes
