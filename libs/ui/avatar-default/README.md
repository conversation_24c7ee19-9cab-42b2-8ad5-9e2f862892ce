# @fincloud/ui/avatar-default

Angular avatar component for displaying user profile images and initials.

## Components

### FinAvatarDefaultComponent (`fin-avatar-default`)

Displays user avatars with automatic fallback to initials.

**Basic Usage:**

```typescript
import { FinAvatarDefaultModule } from '@fincloud/ui/avatar-default';

<fin-avatar-default
  [firstName]="'John'"
  [lastName]="'Doe'"
  [imageUrl]="userImageUrl"
  size="m">
</fin-avatar-default>
```

**Key Features:**

- Image display with fallback to initials
- Multiple sizes (XS, S, M, L, XL)
- Automatic color generation
- Responsive design
