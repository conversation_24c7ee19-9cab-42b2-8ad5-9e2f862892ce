# @fincloud/ui/radio

A comprehensive Angular radio button component library providing single-selection form controls with group and standalone modes, custom styling, and full accessibility support.

## Components

### FinRadioComponent (`fin-radio-button`)

The main radio button component that allows users to select one option from a set with support for both grouped and standalone modes.

**Key Features:**

- Single selection from multiple options
- Group mode for radio button sets
- Standalone mode for individual radio buttons
- Multiple sizes (S, M)
- Custom label positioning (before/after)
- Reactive forms integration with ControlValueAccessor
- Custom property names for label and value
- Built-in validation state display
- Full accessibility support with ARIA attributes
- Built on Angular Material Radio

**Basic Usage (Group Mode):**

```typescript
import { FinRadioModule } from '@fincloud/ui/radio';

// In component
interface RadioOption {
  value: string;
  label: string;
}

radioOptions: RadioOption[] = [
  { value: 'option1', label: 'First Option' },
  { value: 'option2', label: 'Second Option' },
  { value: 'option3', label: 'Third Option' }
];

selectionControl = new FormControl('option1');

// In template
<fin-radio-button
  [options]="radioOptions"
  [formControl]="selectionControl"
  size="m"
  (selectionChanged)="onSelectionChange($event)">
</fin-radio-button>
```

**Standalone Mode:**

```typescript
// Individual radio buttons with shared FormControl
<fin-radio-button
  standalone
  value="small"
  label="Small Size"
  [formControl]="sizeControl">
</fin-radio-button>

<fin-radio-button
  standalone
  value="medium"
  label="Medium Size"
  [formControl]="sizeControl">
</fin-radio-button>

<fin-radio-button
  standalone
  value="large"
  label="Large Size"
  [formControl]="sizeControl">
</fin-radio-button>
```

**Custom Property Names:**

```typescript
// Custom data structure
interface CustomOption {
  id: string;
  name: string;
  description: string;
}

customOptions: CustomOption[] = [
  { id: 'basic', name: 'Basic Plan', description: 'For individuals' },
  { id: 'pro', name: 'Pro Plan', description: 'For teams' },
  { id: 'enterprise', name: 'Enterprise Plan', description: 'For organizations' }
];

// Use custom property names
<fin-radio-button
  [options]="customOptions"
  labelPropertyName="name"
  valuePropertyName="id"
  [formControl]="planControl">
</fin-radio-button>
```

**Label Positioning:**

```typescript
// Labels before radio buttons
<fin-radio-button
  [options]="options"
  [formControl]="control"
  [labelPosition]="labelPosition.BEFORE">
</fin-radio-button>

// Labels after radio buttons (default)
<fin-radio-button
  [options]="options"
  [formControl]="control"
  [labelPosition]="labelPosition.AFTER">
</fin-radio-button>
```

## Models & Interfaces

### FinRadioOption

The interface for radio button options with flexible property structure.

```typescript
interface FinRadioOption {
  [name: string]: unknown;
}

// Common usage patterns:
interface StandardOption extends FinRadioOption {
  value: string | number;
  label: string;
  disabled?: boolean;
}

interface ExtendedOption extends FinRadioOption {
  id: string;
  name: string;
  description?: string;
  icon?: string;
}
```

## Configuration Options

### Size Options

- `S` - Small radio buttons for compact layouts
- `M` - Medium radio buttons (default) for standard forms

### Label Positioning

- `LabelPosition.AFTER` - Labels appear after radio buttons (default)
- `LabelPosition.BEFORE` - Labels appear before radio buttons

### Property Customization

- `labelPropertyName` - Custom property name for option labels (default: 'label')
- `valuePropertyName` - Custom property name for option values (default: 'value')

## Installation & Import

```typescript
import { FinRadioModule } from '@fincloud/ui/radio';

// Or import individual components
import { FinRadioComponent, FinRadioOption } from '@fincloud/ui/radio';
```

## Usage Patterns

### Form Integration

```typescript
// In reactive forms
<form [formGroup]="userForm">
  <fin-radio-button
    [options]="genderOptions"
    formControlName="gender"
    size="m">
  </fin-radio-button>

  <fin-radio-button
    [options]="subscriptionOptions"
    formControlName="subscription"
    size="s">
  </fin-radio-button>
</form>
```

### Validation

```typescript
// With validation
genderControl = new FormControl('', Validators.required);

<fin-radio-button
  [options]="genderOptions"
  [formControl]="genderControl">
</fin-radio-button>

// Validation state is automatically displayed
```

### Dynamic Options

```typescript
// Dynamic option loading
<fin-radio-button
  [options]="dynamicOptions$ | async"
  [formControl]="selectionControl"
  (selectionChanged)="onOptionSelected($event)">
</fin-radio-button>
```

### Conditional Display

```typescript
// Conditional radio groups
<fin-radio-button
  *ngIf="showPaymentOptions"
  [options]="paymentMethods"
  [formControl]="paymentControl">
</fin-radio-button>
```

## Advanced Features

### Form Integration

- **Reactive Forms**: Full integration with Angular Reactive Forms
- **Validation**: Automatic validation state display with error styling
- **Disabled State**: Support for disabled radio buttons via FormControl
- **Value Accessor**: Implements ControlValueAccessor for seamless form integration

### Event Handling

- **Selection Changed**: `selectionChanged` event emitter for custom logic
- **Form Events**: Integration with Angular form events (valueChanges, statusChanges)
- **Validation Events**: Automatic handling of validation state changes

### Accessibility

- **ARIA Support**: Full ARIA attributes for screen readers
- **Keyboard Navigation**: Standard keyboard interaction patterns (arrow keys, space)
- **Focus Management**: Proper focus indicators and management
- **Label Association**: Proper label-input association for assistive technologies
- **Group Semantics**: Proper radio group semantics for screen readers

### Styling & Theming

- **Size Variations**: Multiple size options for different layouts
- **Custom Colors**: Support for theme colors and custom styling
- **Hover States**: Interactive hover and focus states
- **Validation States**: Visual feedback for invalid/touched states
- **Responsive Design**: Consistent appearance across different screen sizes

### Performance

- **Change Detection**: OnPush strategy for optimal performance
- **Track By Function**: Efficient list rendering with trackBy
- **Memory Management**: Proper cleanup and no memory leaks
- **Lazy Evaluation**: Efficient option rendering and updates
