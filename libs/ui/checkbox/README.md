# @fincloud/ui/checkbox

A comprehensive Angular checkbox component library providing form field components for single and multiple selection with advanced features like indeterminate state, custom labels, and accessibility support.

## Components

### FinCheckboxComponent (`fin-checkbox`)

The main checkbox component that allows users to select one or more options from a set with extensive customization and form integration capabilities.

**Key Features:**

- Single and multiple selection support
- Indeterminate (mixed) state for parent-child relationships
- Custom label positioning (before/after)
- Multiple sizes (S, M)
- Custom label content projection
- Reactive forms integration with ControlValueAccessor
- Built-in validation state display
- Text truncation for long labels
- Full accessibility support with ARIA attributes
- Built on Angular Material Checkbox

**Basic Usage:**

```typescript
import { FinCheckboxModule } from '@fincloud/ui/checkbox';

// In component
checkboxControl = new FormControl(false);

// In template
<fin-checkbox
  label="Accept terms and conditions"
  [formControl]="checkboxControl"
  size="m"
  (changed)="onCheckboxChange($event)">
</fin-checkbox>
```

**Custom Label Content:**

```typescript
<fin-checkbox [formControl]="checkboxControl">
  <ng-container finCheckboxLabel>
    <span class="custom-label">
      I agree to the
      <a href="/terms" target="_blank">Terms of Service</a>
      and
      <a href="/privacy" target="_blank">Privacy Policy</a>
    </span>
  </ng-container>
</fin-checkbox>
```

**Indeterminate State:**

```typescript
// In component
parentCheckbox = new FormControl(false);
childCheckboxes = [
  new FormControl(false),
  new FormControl(true),
  new FormControl(false)
];

// In template
<fin-checkbox
  label="Select All"
  [formControl]="parentCheckbox"
  finIndeterminate
  [childControls]="childCheckboxes">
</fin-checkbox>

<div class="child-checkboxes">
  <fin-checkbox
    *ngFor="let control of childCheckboxes; let i = index"
    [label]="'Option ' + (i + 1)"
    [formControl]="control">
  </fin-checkbox>
</div>
```

## Directives

### FinIndeterminateDirective (`finIndeterminate`)

A directive that automatically manages the indeterminate state of a parent checkbox based on the state of child checkboxes.

**Key Features:**

- Automatic indeterminate state calculation
- Parent-child checkbox synchronization
- Reactive updates when child states change
- Support for any number of child controls

**Usage:**

```typescript
<fin-checkbox
  label="Select All Items"
  [formControl]="parentControl"
  finIndeterminate
  [childControls]="childControls">
</fin-checkbox>
```

**How it works:**

- When some (but not all) child checkboxes are checked, the parent shows indeterminate state
- When all child checkboxes are checked, the parent becomes checked
- When no child checkboxes are checked, the parent becomes unchecked
- Checking/unchecking the parent updates all child checkboxes accordingly

## Installation & Import

```typescript
import { FinCheckboxModule } from '@fincloud/ui/checkbox';

// Or import individual components and directives
import { FinCheckboxComponent, FinIndeterminateDirective } from '@fincloud/ui/checkbox';
```

## Checkbox Sizes

- `S` - Small checkbox for compact layouts
- `M` - Medium checkbox (default) for standard forms

## Label Positioning

- `LabelPosition.AFTER` - Label appears after the checkbox (default)
- `LabelPosition.BEFORE` - Label appears before the checkbox

## Advanced Features

### Form Integration

- **Reactive Forms**: Full integration with Angular Reactive Forms
- **Validation**: Automatic validation state display with error styling
- **Disabled State**: Support for disabled checkboxes via FormControl
- **Value Accessor**: Implements ControlValueAccessor for seamless form integration

### Custom Styling

- **Size Variations**: Choose between small and medium sizes
- **Label Truncation**: Automatic text truncation for long labels with `finTruncateText`
- **Custom Content**: Use content projection for complex label layouts
- **Validation States**: Visual feedback for invalid/touched states

### Accessibility

- **ARIA Support**: Full ARIA attributes for screen readers
- **Keyboard Navigation**: Standard keyboard interaction patterns
- **Focus Management**: Proper focus indicators and management
- **Label Association**: Proper label-input association for assistive technologies

### State Management

- **Indeterminate State**: Three-state checkbox support (checked, unchecked, indeterminate)
- **Event Handling**: `changed` event emitter for custom logic
- **Programmatic Control**: Methods to set indeterminate state programmatically
- **Reactive Updates**: Automatic UI updates based on FormControl changes
