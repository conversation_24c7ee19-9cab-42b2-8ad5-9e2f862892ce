# @fincloud/ui/modal

A comprehensive Angular modal dialog library providing overlay components for displaying content, forms, confirmations, and complex interactions in a focused overlay window.

## Components & Services

### FinModalService

The main service for programmatically opening and managing modal dialogs with extensive configuration options.

**Key Features:**

- Programmatic modal creation and management
- Data injection into modal components
- Multiple modal sizes (S, M, L, XL, XXL)
- Backdrop and close behavior configuration
- Modal reference management for communication
- Built on Angular Material Dialog with enhanced features

**Basic Usage:**

```typescript
import { FinModalService } from '@fincloud/ui/modal';

constructor(private modalService: FinModalService) {}

openModal() {
  const modalRef = this.modalService.open(MyModalComponent, {
    data: { message: 'Hello from parent!' },
    size: FinSize.L,
    hasBackdrop: true,
    disableClose: false
  });

  modalRef.afterClosed().subscribe(result => {
    console.log('Modal closed with result:', result);
  });
}
```

### FinModalSlotsContainerComponent (`fin-modal-slots-container`)

A container component that provides structured layout slots for modal content organization.

**Key Features:**

- Predefined layout structure
- Header, content, and footer slots
- Consistent modal appearance
- Flexible content projection

### Modal Directives

#### FinModalHeaderDirective (`fin-modal-header`)

Defines the header section of a modal with title and action areas.

**Basic Usage:**

```typescript
<fin-modal-header>
  <fin-header>
    Modal Title
    <button fin-button-action size="l" fin-modal-close>
      <fin-icon name="close"></fin-icon>
    </button>
  </fin-header>
</fin-modal-header>
```

#### FinModalContentDirective (`fin-modal-content`)

Defines the main content area of the modal with proper scrolling and spacing.

**Basic Usage:**

```typescript
<fin-modal-content>
  <p>Your modal content goes here...</p>
  <form>
    <!-- Form fields -->
  </form>
</fin-modal-content>
```

#### FinModalFooterDirective (`fin-modal-footer`)

Defines the footer section typically containing action buttons.

**Basic Usage:**

```typescript
<fin-modal-footer>
  <fin-footer>
    <button fin-button appearance="secondary" fin-modal-close>
      Cancel
    </button>
    <button fin-button appearance="primary" (click)="save()">
      Save
    </button>
  </fin-footer>
</fin-modal-footer>
```

#### FinModalCloseDirective (`fin-modal-close`)

A directive that automatically closes the modal when applied to buttons or clickable elements.

**Basic Usage:**

```typescript
<button fin-button fin-modal-close>Close</button>
```

#### FinModalSlotDirective (`fin-modal-slot`)

Provides flexible content projection for custom modal layouts.

## Installation & Import

```typescript
import { FinModalModule, FinModalService } from '@fincloud/ui/modal';

// For data injection
import { FIN_MODAL_DATA } from '@fincloud/ui/modal';

// In modal component
constructor(@Inject(FIN_MODAL_DATA) private data: any) {}
```

## Modal Configuration

### FinModalConfig

Configuration object for customizing modal behavior and appearance.

**Properties:**

- `data: any` - Data to inject into the modal component
- `size: FinSize` - Modal size (S: 410px, M: 600px, L: 800px, XL: 1024px, XXL: fullscreen)
- `hasBackdrop: boolean` - Whether to show backdrop overlay
- `disableClose: boolean` - Prevent closing via ESC or backdrop click

### Modal Sizes

- `S` - Small modal (410px width)
- `M` - Medium modal (600px width) - default
- `L` - Large modal (800px width)
- `XL` - Extra large modal (1024px width)
- `XXL` - Full screen modal (100% width and height)

## Advanced Features

- **Data Communication**: Pass data to modal components and receive results on close
- **Modal References**: Manage multiple modals and their lifecycle
- **Accessibility**: Full ARIA support and keyboard navigation
- **Backdrop Control**: Configurable backdrop behavior and styling
- **Responsive Design**: Automatic sizing adjustments for different screen sizes
- **Animation**: Smooth open/close animations with Material Design principles
- **Nested Modals**: Support for opening modals from within other modals
