# @fincloud/ui/search

Angular search input component with autocomplete functionality.

## Components

### FinSearchComponent (`fin-search`)

Search input field with optional autocomplete dropdown.

**Basic Usage:**

```typescript
import { FinSearchModule } from '@fincloud/ui/search';

<fin-search
  placeholder="Search..."
  [formControl]="searchControl"
  [autocomplete]="true"
  [options]="searchOptions"
  (inputChange)="onSearch($event)">
</fin-search>
```

**Key Features:**

- Search input with icon
- Autocomplete with custom options
- Custom message templates
- Form integration
