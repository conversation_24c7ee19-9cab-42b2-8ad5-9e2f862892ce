{"name": "@fincloud/ui", "version": "0.0.0", "license": "MIT", "publishConfig": {"@fincloud:registry": "https://nexus-v2.neo.loan/repository/npm/", "access": "restricted"}, "repository": {"type": "git", "url": "https://git.neo.loan/fincloud/lib-ui.git"}, "author": "Neoshare", "scripts": {"build": "nx run ui:prepare-publish", "build:storybook": "npx nx run ui:build-storybook:ci", "lint": "nx run-many --target=lint --projects=ui,utils", "lint:fix": "npx nx run-many --target=lint --quiet --fix --all --exclude=fincloud-ui", "postinstall": "node npm-postinstall.js", "publish": "nx release patch publish"}, "dependencies": {"@angular/animations": "17.3.12", "@angular/cdk": "17.3.10", "@angular/common": "17.3.12", "@angular/compiler": "17.3.12", "@angular/core": "17.3.12", "@angular/forms": "17.3.12", "@angular/material": "17.3.10", "@angular/platform-browser": "17.3.12", "@angular/platform-browser-dynamic": "17.3.12", "@angular/router": "17.3.12", "@ng-bootstrap/ng-bootstrap": "16.0.0", "@nx/angular": "19.0.8", "@storybook/addon-themes": "8.4.4", "angular-svg-icon": "17.0.0", "change-case": "5.4.4", "chart.js": "4.4.6", "date-fns": "4.1.0", "eslint-plugin-import": "2.29.1", "lodash-es": "4.17.21", "ngx-autosize": "2.0.4", "ngx-mask": "19.0.6", "pluralize": "8.0.0", "primeng": "17.18.15", "react": "18.2.0", "react-dom": "18.2.0", "rxjs": "7.8.1", "storybook-version": "0.1.2", "tslib": "2.5.0", "uuid": "9.0.1", "vsce": "2.15.0", "zone.js": "0.14.6"}, "devDependencies": {"@angular-devkit/build-angular": "17.3.8", "@angular-devkit/core": "17.3.8", "@angular-devkit/schematics": "17.3.8", "@angular-eslint/eslint-plugin": "17.5.2", "@angular-eslint/eslint-plugin-template": "17.5.2", "@angular-eslint/template-parser": "17.5.2", "@angular/cli": "17.3.8", "@angular/compiler-cli": "17.3.12", "@angular/language-service": "17.3.12", "@angular/localize": "17.3.12", "@chromatic-com/storybook": "3.2.2", "@compodoc/compodoc": "1.1.26", "@fincloud/eslint-plugin-ns": "1.5.4", "@fincloud/lib-git-hooks": "4.0.6", "@fincloud/lib-ui-tokens": "1.0.35", "@nx/devkit": "19.0.8", "@nx/eslint": "19.0.8", "@nx/eslint-plugin": "19.0.8", "@nx/jest": "19.0.8", "@nx/js": "19.0.8", "@nx/playwright": "19.0.8", "@nx/storybook": "19.0.8", "@nx/web": "19.0.8", "@nx/workspace": "19.0.8", "@playwright/test": "1.36.0", "@schematics/angular": "17.3.8", "@storybook/addon-designs": "8.0.4", "@storybook/addon-essentials": "8.4.4", "@storybook/addon-interactions": "8.4.4", "@storybook/angular": "8.4.4", "@storybook/core-server": "8.4.4", "@storybook/jest": "0.2.3", "@storybook/preview-api": "8.4.4", "@storybook/test": "8.4.4", "@storybook/test-runner": "0.19.1", "@storybook/testing-library": "0.2.2", "@swc-node/register": "1.8.0", "@swc/core": "1.9.2", "@swc/helpers": "0.5.2", "@types/jest": "29.4.0", "@types/lodash-es": "4.17.12", "@types/node": "20.14.2", "@typescript-eslint/eslint-plugin": "7.12.0", "@typescript-eslint/parser": "7.12.0", "autoprefixer": "10.4.19", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-playwright": "0.15.3", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-storybook": "0.11.0", "glob": "11.0.0", "jest": "29.5.0", "jest-environment-jsdom": "29.5.0", "jest-preset-angular": "14.0.3", "jsonc-eslint-parser": "2.1.0", "lefthook": "1.6.9", "ng-packagr": "17.3.0", "ngx-currency": "17.0.0", "ngx-infinite-scroll": "17.0.1", "ngx-scrollbar": "16.1.0", "nx": "19.0.8", "postcss": "8.4.5", "postcss-url": "10.1.3", "prettier": "3.3.3", "prettier-plugin-organize-imports": "4.0.0", "prettier-plugin-tailwindcss": "0.4.1", "shelljs": "0.8.5", "tailwindcss": "3.4.13", "tailwindcss-themer": "4.0.0", "ts-jest": "29.1.0", "ts-morph": "25.0.0", "ts-node": "10.9.2", "typescript": "5.4.5"}, "overrides": {"stylus": "0.0.1-security"}}